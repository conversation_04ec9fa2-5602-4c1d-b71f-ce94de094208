import requests
import re
import json
import pandas as pd
from collections import OrderedDict
from config import ENDPOINT_URL, MODEL_NAME, API_KEY, API_QPM, API_TPM, BATCH_COUNT,LEVEL2_NAME
from csv_2_xls import process_csv_to_excel
import time
import math  # 用于token估算

# 全局限流状态变量
qpm_counter = 0
tpm_counter = 0
window_start_time = time.time()

def call_qwen3(prompt, user_input):
    global qpm_counter, tpm_counter, window_start_time  # 使用全局限流变量
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    # 构造消息体
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": user_input}
    ]
    stream = True
    data = {
        "model": MODEL_NAME,
        "messages": messages,
        "temperature": 0.1,
        "stream": stream,
        "enable_thinking": False
    }

    # 新增限流逻辑
    current_time = time.time()
    # 重置窗口
    if current_time - window_start_time >= 60:
        qpm_counter = 0
        tpm_counter = 0
        window_start_time = current_time
    
    # 输入token估算（中文按2token/字符，英文按1token/单词）
    input_text = prompt + user_input
    input_token_count = math.ceil(len(input_text) * 1.5)  # 粗略估算
    
    # QPM/TPM等待逻辑
    while qpm_counter >= API_QPM or tpm_counter + input_token_count > API_TPM:
        sleep_time = max(60 - (current_time - window_start_time) + 0.1, 0)
        print(f"达到限流阈值，等待{sleep_time:.1f}秒...")
        time.sleep(sleep_time)
        current_time = time.time()
        if current_time - window_start_time >= 60:
            qpm_counter = 0
            tpm_counter = 0
            window_start_time = current_time
    
    # 更新计数器
    qpm_counter += 1
    tpm_counter += input_token_count
    
    content = ""
    with requests.post(ENDPOINT_URL, headers=headers, json=data, stream=stream) as response:
        response.raise_for_status()
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith("data: "):
                    try:
                        chunk = json.loads(decoded_line[6:])  # 去除"data: "前缀并解析
                        content += chunk["choices"][0]["delta"].get("content", "")
                    except json.JSONDecodeError:
                        continue
    
    # 输出token估算
    output_token_count = math.ceil(len(content) * 1.5)
    tpm_counter += output_token_count
    
    return extract_json_from_content(content)

def extract_json_from_content(content):
    """
    从大模型返回的 content 字符串中提取并解析 JSON 代码块
    """
    # 用正则表达式提取 ```json ... ``` 之间的内容
    # print(f"解析json: {content}")
    match = re.search(r"``json\s*(.*?)\s*```", content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            # 解析为 Python 对象
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print("JSON解析失败：", e)
            return None
    else:
        print(f"未找到JSON代码块：{content}")
        return None

def flatten_results_with_subprocess(all_items):
    """
    输入：all_items为包含模块信息和大模型返回的所有item（每个item有'子过程'字段，值为列表）
    输出：每个子过程一行的扁平化结果，所有父级信息每行都保留
    """
    flat_results = []
    for item in all_items:
        subprocess_list = item.get('子过程', [])
        # 拷贝除'子过程'外的所有字段
        base_info = {k: v for k, v in item.items() if k != '子过程'}
        for subprocess in subprocess_list:
            row = base_info.copy()
            row.update(subprocess)  # 子过程的key直接变成列
            flat_results.append(row)
    return flat_results

def set_duplicate_modules_to_nan(df, module_cols, right_cols):
    """
    对module_cols做全局去重，对right_cols只在同一个三级模块下做去重。
    """
    # 1. 先对module_cols做全局去重
    for col in module_cols:
        df[col] = df[col].where(df[col] != df[col].shift(), None)
    # 2. 对right_cols在同一个三级模块下做去重
    level_3_col = module_cols[-1]  # 假设最后一个是三级模块
    for col in right_cols:
        if col in df.columns:
            df[col] = df.groupby(level_3_col, group_keys=False)[col].apply(lambda x: x.where(x != x.shift(), None))
    return df


if __name__ == "__main__":
    excel_file = "附件4：中国移动河南公司2025年移动爱家终端管理平台研发项目-软件功能清单.xlsx"
    level_1_name, level_2_name, level_3_name = "一级功能模块", "二级功能模块","三级功能模块"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量（人天）"
    sheet_name = 1
    header = 0

    with open('prompt.md', 'r', encoding='utf-8') as f:
        prompt = f.read()
    
    df = pd.read_excel(excel_file, sheet_name = sheet_name, header = header)
    
    # 列名替换逻辑 : 预估工作量列中有回车符
    # df.columns = [col if not re.search(r'预估工作量', col) else estimated_workload_name for col in df.columns]
    
    # 向后填充一级/二级模块列      
    df[[level_1_name, level_2_name]] = df[[level_1_name, level_2_name]].ffill()

    all_results = []
    user_inputs = []
    current_level_2 = None
    current_level_1 = None
    current_level_3_list = []
    current_function_description_list = []
    current_estimated_workload_list = []
    current_function_description = None
    current_estimated_workload = None
    count = 0  # 计数器
    
    for idx, row in df.iterrows():
        level_1_module = str(row[level_1_name])
        level_2_module = str(row[level_2_name])
        level_3_module = str(row[level_3_name])
        function_description = str(row[function_description_name])
        estimated_workload = str(row[estimated_workload_name])
        # 跳过无效三级模块
        if pd.isna(level_3_module) or str(level_3_module).strip().lower() in ['nan', 'none', '']:
            continue

        # 如果遇到新的二级功能模块，先处理上一个
        if (current_level_2 is not None) and (level_2_module != current_level_2 or level_1_module != current_level_1):
            #print(current_level_3_list)
            #print(current_function_description_list)
            #print(current_estimated_workload_list)
            # user_inputs按BATCH_COUNT拆分处理
            for i in range(0, int(len(user_inputs) / BATCH_COUNT) + 1):
                # 累计user_input字符串
                start_row = i * BATCH_COUNT
                end_row = min((i + 1) * BATCH_COUNT, len(user_inputs))
                batch_user_inputs = user_inputs[start_row:end_row]
                #batch_user_input = "\n".join(batch_user_inputs)
                batch_user_input = "\n".join([f"{i+1}. {input}" for i, input in enumerate(batch_user_inputs)])
                #print(f"【{current_level_1}-{current_level_2}】累计user_input：\n{batch_user_input}")
                result = None
                if LEVEL2_NAME and current_level_2 != LEVEL2_NAME:
                    continue
                else:
                    print(f"开始处理:【{current_level_1}-{current_level_2}-{i}......")
                    result = call_qwen3(prompt, batch_user_input)
                #print(json.dumps(result, ensure_ascii=False, indent=4))
                #print("="*50)
                # 结果处理
                if result and isinstance(result, list):
                    for item, level_3, func_desc, est_work in zip(result, current_level_3_list[start_row:end_row], current_function_description_list[start_row:end_row], current_estimated_workload_list[start_row:end_row]):
                        ordered_item = OrderedDict()
                        ordered_item[level_1_name] = current_level_1
                        ordered_item[level_2_name] = current_level_2
                        ordered_item[level_3_name] = level_3
                        ordered_item[function_description_name] = func_desc
                        ordered_item[estimated_workload_name] = est_work
                        for k, v in item.items():
                            ordered_item[k] = v 
                        all_results.append(ordered_item)
                    #break
            
            # 清空累计
            user_inputs = []
            current_level_3_list = []
            current_function_description_list = []
            current_estimated_workload_list = []
            #break
            
        
        # 累计当前三级功能模块的user_input
        user_input = f"{level_1_name}：{level_1_module}，{level_2_name}：{level_2_module}，{level_3_name}：{level_3_module}, {function_description_name}: {function_description}, {estimated_workload_name}: {estimated_workload}"
        user_inputs.append(user_input)

        current_level_3_list.append(level_3_module)
        current_function_description_list.append(function_description)
        current_estimated_workload_list.append(estimated_workload)

        current_level_3 = level_3_module
        current_level_2 = level_2_module
        current_level_1 = level_1_module
        current_function_description = function_description
        current_estimated_workload = estimated_workload
    
    # 循环结束后，处理最后一组
    """
    if user_inputs:
        batch_user_input = "\n".join(user_inputs)
        print(f"【{current_level_1}-{current_level_2}】累计user_input：\n{batch_user_input}")
        result = call_qwen3(prompt, batch_user_input)
        print(json.dumps(result, ensure_ascii=False, indent=4))
        print("="*50)
        if result and isinstance(result, list):
            for item in result:
                ordered_item = OrderedDict()
                ordered_item[level_1_name] = current_level_1
                ordered_item[level_2_name] = current_level_2
                ordered_item[function_description_name] = current_function_description
                ordered_item[estimated_workload_name] = current_estimated_workload
                for k, v in item.items():
                    ordered_item[k] = v
    """
   
    
    flat_results = flatten_results_with_subprocess(all_results)
    result_df = pd.DataFrame(flat_results)
    module_cols = [level_1_name, level_2_name, level_3_name]
    right_cols = [function_description_name, estimated_workload_name, '功能用户', '触发事件', '功能过程']
    result_df = set_duplicate_modules_to_nan(result_df, module_cols, right_cols)
    result_df.to_csv('output.csv', index=False, encoding='utf-8-sig')
    process_csv_to_excel('output.csv')