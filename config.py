# 每批次最多的三级模块数量
BATCH_COUNT = 35
LEVEL2_NAME = "密码资产数据管理"
ENDPOINT_URL="http://10.10.43.64:3300/v1/chat/completions"
#ENDPOINT_URL="https://ai.secsign.online:3003/v1/chat/completions"
MODEL_NAME="qwen3-32b"
API_KEY="sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"

# 阿里云
# ENDPOINT_URL="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
# ## ENDPOINT_URL="https://10.10.43.64:3000/compatible-mode/v1/chat/completions"
# MODEL_NAME="Moonshot-Kimi-K2-Instruct"
# API_KEY="sk-1d06e75d7fd94338b5b32cf8f9099651"
# 每分钟的调用次数限制
API_QPM=60
# 每分钟处理的文本token数量
API_TPM=100000

# openrouter
# ENDPOINT_URL="https://openrouter.ai/api/v1/chat/completions"
# MODEL_NAME="moonshotai/kimi-k2:free"
# API_KEY="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"
# # 每分钟的调用次数限制
# API_QPM=1
# # 每分钟处理的文本token数量
# API_TPM=1000000