﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
密码资产数据管理,密码资产数据管理,密码服务数据库新增,新增数据库信息，选择数据库类型，输入数据库IP、端口，管理员账号、密码,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在数据库管理页面点击新增按钮,新增密码服务数据库,输入数据库基础信息,E,数据库信息,数据库类型、IP地址、端口号,1
,,,,,,,,输入管理员凭证信息,E,管理员信息,管理员账号、管理员密码,1
,,,,,,,,验证数据库连接状态,R,数据库连接状态,连接状态、错误信息,1
,,,,,,,,保存数据库配置信息,W,数据库配置,数据库类型、IP地址、端口号、管理员账号、管理员密码,1
,,密码服务数据库列表,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在数据库管理页面点击列表刷新按钮,展示密码服务数据库列表,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取数据库基础信息,R,数据库列表,数据库名称、数据库类型、实例库名称、IP地址、端口号,1
,,,,,,,,读取完整性校验状态,R,校验信息,校验状态、校验时间,1
,,,,,,,,输出数据库列表数据,X,数据库列表,数据库名称、数据库类型、实例库名称、IP地址、端口号、校验状态,1
,,密码服务数据库模式列表,列表展示密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在数据库模式管理页面点击查询按钮,查询密码服务数据库模式,输入模式查询条件,E,查询条件,模式名称、数据库名称,1
,,,,,,,,读取数据库模式信息,R,数据库模式,模式名称、模式描述、创建时间,1
,,,,,,,,输出数据库模式列表,X,数据库模式,模式名称、模式描述、创建时间,1
,,密码服务数据库模式删除,删除密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在数据库模式管理页面点击删除按钮,删除密码服务数据库模式,选择待删除模式,E,模式信息,模式名称、数据库名称,1
,,,,,,,,验证模式关联状态,R,关联信息,关联服务数量、关联设备数量,1
,,,,,,,,执行模式删除操作,W,模式信息,模式名称、数据库名称,1
,,密码服务数据库模式查询,查询密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在API网关管理页面点击初始化按钮,初始化API网关配置,读取平台部署信息,R,部署信息,网关类型、部署区域、IP地址,1
,,,,,,,,生成默认网关配置,W,网关配置,网关名称、标识、类型、IP地址,1
,,密码服务数据库模式新增,新增数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在设备类型管理页面点击新增按钮,新增设备类型,输入设备类型基础信息,E,设备类型信息,设备类型名称、所属厂商、设备类型分类,1
,,,,,,,,输入管理接口配置,E,接口配置,管理接口协议、管理端口号,1
,,,,,,,,保存设备类型配置,W,设备类型配置,设备类型名称、所属厂商、设备类型分类、管理接口协议、管理端口号,1
,,API网关列表,列表内容：名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP（列表不显示）、端口（列表不显示）、反向代理地址端口（列表不显示）,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机管理页面点击新建按钮,新建云密码机,输入云密码机基础信息,E,云密码机信息,云密码机名称、管理IP、管理端口,1
,,,,,,,,输入网络配置信息,E,网络配置,虚拟机网络标识、IP地址范围,1
,,,,,,,,验证IP地址有效性,R,IP验证结果,IP有效性、冲突状态,1
,,,,,,,,保存云密码机配置,W,云密码机配置,云密码机名称、管理IP、管理端口、虚拟机网络标识、IP地址范围,1
,,API网关初始化,密码服务平台部署成功后，如选择部署API网关，根据平台部署信息，自动加载对应的部署网关信息,3.0,发起者：管理员，接收者：密码资产管理系统,管理员在虚拟机网络配置页面点击新增按钮,新增虚拟机网络配置,输入网络地址范围,E,网络地址,管理IP范围、业务IP范围,1
,,,,,,,,输入端口范围配置,E,端口配置,管理端口范围、业务端口范围,1
,,,,,,,,验证IP地址冲突,R,冲突检测,冲突IP列表、可用IP数量,1
,,,,,,,,保存网络配置信息,W,网络配置,管理IP范围、业务IP范围、管理端口范围、业务端口范围,1
,,批量创建虚拟机,批量创建虚拟密码机，自动加载虚机网络，支持配置虚机资源。调用云密码机0088标准创建虚机并自动配置网络。,10.0,发起者：管理员，接收者：密码资产管理平台,管理员在批量创建页面点击创建按钮,批量创建虚拟密码机,输入批量创建参数,E,批量创建参数,虚拟机数量、资源配置、网络模板,1
,,,,,,,,调用云密码机0088标准API,X,创建请求,API参数、认证信息,1
,,,,,,,,读取网络配置模板,R,网络配置模板,子网ID、安全组规则,1
,,,,,,,,写入虚拟机实例状态,W,虚拟机实例,实例ID、创建状态,1
,,,,,,,,输出创建结果,X,创建结果,成功/失败标识、错误信息,1
,,,,,,,,记录操作日志,W,操作日志,操作时间、操作人、操作类型,1
,,,,,,,,更新资源配额,W,资源配额,已用CPU、内存、存储,1
,,,,,,,,发送通知消息,X,通知消息,接收人、消息内容,1
,,,,,,,,校验输入参数有效性,E,参数校验规则,必填项、格式约束,1
,,,,,,,,生成虚拟机唯一标识,W,虚拟机标识,UUID、命名规则,1
,,虚拟密码机列表,平台中云机虚拟出的VSM,4.0,发起者：管理员，接收者：密码资产管理平台,管理员访问虚拟密码机列表页面,展示虚拟密码机列表,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取虚拟机列表数据,R,虚拟机列表,虚拟机名称、状态、IP地址,1
,,,,,,,,输出列表展示数据,X,展示数据,表格结构、状态图标,1
,,虚拟密码机列表查询,支持根据名称、主机、管理ip、服务ip、设备类型进行查询,2.0,发起者：管理员，接收者：密码资产管理平台,管理员在列表页面输入查询条件,查询虚拟密码机,输入查询条件,E,查询条件,名称、主机、管理IP、服务IP、设备类型,1
,,,,,,,,读取匹配的虚拟机数据,R,虚拟机数据,虚拟机名称、IP地址、状态,1
,,,,,,,,输出查询结果,X,查询结果,表格数据、匹配数量,1
,,创建虚拟密码机,选择云密码机，批量创建虚拟密码机，和云机管理中批量创建密码机一致,2.0,发起者：管理员，接收者：密码资产管理平台,管理员点击创建虚拟密码机按钮,创建单个虚拟密码机,选择云密码机类型,E,云密码机类型,型号、规格,1
,,,,,,,,输入虚拟机配置参数,E,配置参数,CPU、内存、存储,1
,,,,,,,,调用创建接口,X,创建请求,API参数、认证信息,1
,,虚拟密码机详情,虚拟密码机详情,5.0,发起者：管理员，接收者：密码资产管理平台,管理员点击虚拟密码机详情按钮,展示虚拟密码机详情,读取虚拟机基础信息,R,虚拟机基础信息,名称、IP地址、创建时间,1
,,,,,,,,读取虚拟机资源信息,R,虚拟机资源,CPU使用率、内存占用,1
,,,,,,,,输出详情展示数据,X,详情数据,卡片结构、状态标签,1
,,编辑虚拟密码机,编辑虚拟密码机名称、连接密码，并在动态下发给密码服务,4.0,发起者：管理员，接收者：密码资产管理平台,管理员在编辑页面修改虚拟机信息,编辑虚拟密码机,输入修改后的名称,E,名称信息,新名称,1
,,,,,,,,输入新的连接密码,E,连接密码,密码、确认密码,1
,,,,,,,,更新虚拟机信息,W,虚拟机信息,名称、连接密码,1
,,,,,,,,动态下发配置到服务,X,配置指令,更新标识、参数,1
,,删除虚拟密码机,删除虚拟密码机,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击删除按钮,删除虚拟密码机,确认删除操作,E,确认信息,确认标识,1
,,,,,,,,执行删除操作,W,虚拟机状态,删除标记,1
,,启动虚拟密码机,启动虚拟密码机,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击启动按钮,启动虚拟密码机,发送启动指令,X,控制指令,启动标识,1
,,,,,,,,更新运行状态,W,虚拟机状态,运行状态,1
,,停止虚拟密码机,停止虚拟密码机,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击停止按钮,停止虚拟密码机,发送停止指令,X,控制指令,停止标识,1
,,,,,,,,更新运行状态,W,虚拟机状态,运行状态,1
,,重启虚拟密码机,重启虚拟密码机,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击重启按钮,重启虚拟密码机,发送重启指令,X,控制指令,重启标识,1
,,,,,,,,更新运行状态,W,虚拟机状态,运行状态,1
,,强制删除虚拟密码机,解决虚拟密码机已不存在，无法正常删除情况,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击强制删除按钮,强制删除虚拟密码机,确认强制删除,E,确认信息,确认标识,1
,,,,,,,,执行强制删除,W,虚拟机状态,删除标记,1
,,生成虚机影像,生成虚机影像,4.0,发起者：管理员，接收者：密码资产管理平台,管理员点击生成影像按钮,生成虚机影像,选择生成配置,E,生成配置,影像格式、存储位置,1
,,,,,,,,执行影像生成,X,生成指令,生成标识,1
,,,,,,,,记录生成状态,W,影像状态,生成进度、结果,1
,,下载虚机影像,下载虚机影像,4.0,发起者：管理员，接收者：密码资产管理平台,管理员点击下载影像按钮,下载虚机影像,请求下载链接,X,下载请求,影像ID,1
,,,,,,,,输出下载链接,X,下载链接,URL地址,1
,,导入虚机影像,导入虚机影像，还原虚机影像,4.0,发起者：管理员，接收者：密码资产管理平台,管理员点击导入影像按钮,导入虚机影像,上传影像文件,E,影像文件,文件内容、元数据,1
,,,,,,,,执行影像还原,X,还原指令,还原标识,1
,,物理密码机列表,物理密码机列表展示已经注册到系统中物理密码机信息，包含密码机的名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注等信息。,4.0,发起者：管理员，接收者：密码资产管理平台,管理员访问物理密码机列表页面,展示物理密码机列表,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取物理机列表数据,R,物理机列表,名称、厂商、设备类型,1
,,,,,,,,输出列表展示数据,X,展示数据,表格结构、状态图标,1
,,物理密码机新建,系统密码机是将机房已经上架部署完毕的密码机注册到系统中，交由管理平台进行统一管理。,5.0,发起者：管理员，接收者：密码资产管理平台,管理员点击新建物理密码机按钮,新建物理密码机,输入物理机基本信息,E,物理机信息,名称、厂商、设备类型,1
,,,,,,,,输入网络配置信息,E,网络配置,管理IP、端口,1
,,,,,,,,调用注册接口,X,注册请求,API参数、认证信息,1
,,物理密码机编辑,在密码机列表页，点击右侧操作列“编辑”按钮，打开物理密码机编辑页面，修改物理密码机信息(可编辑名称、备注、连接密码)后，点击“确定”按钮保存编辑的信息,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击编辑物理密码机按钮,编辑物理密码机,输入修改后的名称,E,名称信息,新名称,1
,,,,,,,,输入新的备注信息,E,备注信息,备注内容,1
,,,,,,,,更新物理机信息,W,物理机信息,名称、备注,1
,,物理密码机删除,在密码机信息列表中可以删除已经注册到系统中的设备信息。,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击删除物理密码机按钮,删除物理密码机,确认删除操作,E,确认信息,确认标识,1
,,,,,,,,执行删除操作,W,物理机状态,删除标记,1
,,物理密码机详情,在密码机列表页，点击右侧操作列“详情”按钮，系统打开密码机详情页面。,5.0,发起者：管理员，接收者：密码资产管理平台,管理员点击物理密码机详情按钮,展示物理密码机详情,读取物理机基础信息,R,物理机基础信息,名称、厂商、设备类型,1
,,,,,,,,读取物理机网络信息,R,物理机网络,管理IP、端口,1
,,,,,,,,输出详情展示数据,X,详情数据,卡片结构、状态标签,1
,,强制删除,解决物理密码机已损坏，无法正常删除情况,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击强制删除物理密码机按钮,强制删除物理密码机,确认强制删除,E,确认信息,确认标识,1
,,,,,,,,执行强制删除,W,物理机状态,删除标记,1
,,管理页面跳转,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击管理页面跳转按钮,跳转管理页面,读取管理页面地址,R,管理地址,URL地址,1
,,,,,,,,输出跳转链接,X,跳转链接,URL地址,1
,,保护主密钥创建,服务器密码机、虚拟服务器密码机、签名验签服务器的保护主密钥的创建,5.0,发起者：管理员，接收者：密码资产管理平台,管理员点击创建保护主密钥按钮,创建保护主密钥,输入密钥参数,E,密钥参数,密钥类型、长度,1
,,,,,,,,生成保护主密钥,X,密钥生成指令,生成标识,1
,,,,,,,,存储密钥信息,W,密钥信息,密钥ID、生成时间,1
,,保护主密钥同步,支持设备内保护主密钥的同步,4.0,发起者：管理员，接收者：密码资产管理平台,管理员点击同步保护主密钥按钮,同步保护主密钥,读取现有密钥,R,现有密钥,密钥ID、内容,1
,,,,,,,,执行同步操作,X,同步指令,同步标识,1
,,,,,,,,更新同步状态,W,密钥状态,同步时间、结果,1
,,保护主密钥备份,将设备内保护主密钥的加密导出备份,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击备份保护主密钥按钮,备份保护主密钥,读取密钥内容,R,密钥内容,密钥ID、加密数据,1
,,,,,,,,执行备份操作,X,备份指令,备份标识,1
,,保护主密钥还原,还原设备内保护主密钥,3.0,发起者：管理员，接收者：密码资产管理平台,管理员点击还原保护主密钥按钮,还原保护主密钥,上传备份文件,E,备份文件,文件内容、元数据,1
,,,,,,,,执行还原操作,X,还原指令,还原标识,1
