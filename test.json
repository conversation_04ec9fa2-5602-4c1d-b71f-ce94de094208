[{"集团平台配置": [{"功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户点击上级平台配置菜单", "功能过程": "查看上级平台配置信息", "子过程": [{"子过程描述": "查询总部平台信息", "数据移动类型": "E", "数据组": "分页信息", "数据属性": "页码、单页数量", "CFP": 1}, {"子过程描述": "读取上级平台信息", "数据移动类型": "R", "数据组": "总部平台信息", "数据属性": "上级平台名称、上级平台IP、上级平台端口", "CFP": 1}]}, {"功能用户": "发起者：用户，接收者：密服平台-集省对接模块", "触发事件": "用户在上级平台管理页面点击上级平台地址配置按钮，配置上级平台地址配置", "功能过程": "配置上级平台信息", "子过程": [{"子过程描述": "输入上级平台信息", "数据移动类型": "E", "数据组": "上级平台信息", "数据属性": "上级平台名称、上级平台IP、上级平台端口", "CFP": 1}]}]}, {"角色权限分配": [{"功能用户": "发起者：管理员，接收者：用户管理系统", "触发事件": "管理员点击角色权限分配菜单", "功能过程": "查看角色权限分配信息", "子过程": [{"子过程描述": "查询角色权限分配信息", "数据移动类型": "E", "数据组": "分页信息", "数据属性": "页码、单页数量", "CFP": 1}, {"子过程描述": "读取角色权限分配信息", "数据移动类型": "R", "数据组": "角色权限分配信息", "数据属性": "角色名称、权限列表", "CFP": 1}]}, {"功能用户": "发起者：管理员，接收者：用户管理系统", "触发事件": "管理员在角色权限分配页面点击分配权限按钮，为角色分配权限", "功能过程": "分配角色权限", "子过程": [{"子过程描述": "选择角色", "数据移动类型": "E", "数据组": "角色信息", "数据属性": "角色名称", "CFP": 1}, {"子过程描述": "选择权限", "数据移动类型": "E", "数据组": "权限信息", "数据属性": "权限名称", "CFP": 1}, {"子过程描述": "保存角色权限分配", "数据移动类型": "W", "数据组": "角色权限分配信息", "数据属性": "角色名称、权限列表", "CFP": 1}]}]}]