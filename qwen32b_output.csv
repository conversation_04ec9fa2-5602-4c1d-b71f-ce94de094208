﻿一级功能模块,二级功能模块,三级功能模块,功能描述,"预估工作量
（人天）",功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
系统管理,总部一级平台对接,总部平台上报路径配置,支持配置总部平台的上报路径，上报路径为集团平台的调用路径。支持ipv4及ipv6，支持对地址格式进行检测校验,4.0,发起者：系统管理员，接收者：系统管理-总部一级平台对接模块,系统管理员在总部平台上报路径配置页面点击查询按钮,查看总部平台上报路径配置信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报路径配置数据,R,上报路径配置信息,路径名称、IP地址、端口号、协议类型、创建时间,1
,,,,,,系统管理员在总部平台上报路径配置页面点击保存按钮,配置总部平台上报路径,输入上报路径信息,E,上报路径信息,路径名称、IP地址、端口号、协议类型,1
,,,,,,,,校验IP地址格式,R,地址校验规则,IPv4格式规则、IPv6格式规则,1
,,,,,,,,执行地址格式校验,X,校验结果,校验状态、错误信息,1
,,,,,,,,保存上报路径配置,W,上报路径配置信息,路径名称、IP地址、端口号、协议类型,1
,,总部平台上报路径查看,展示总部平台上报路径,2.0,发起者：用户，接收者：系统管理-总部一级平台对接模块,用户点击总部平台上报路径查看菜单,查看总部平台上报路径信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取总部平台上报路径数据,R,总部平台上报路径信息,路径名称、路径地址、状态、创建时间,1
,,总部平台HTTPS通道对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3.0,发起者：系统管理员，接收者：系统管理模块-总部平台对接子系统,系统管理员在HTTPS通道配置页面点击保存按钮,配置HTTPS通道参数,输入HTTPS通道配置参数,E,HTTPS通道配置参数,TLS版本、加密套件列表、证书路径、证书有效期,1
,,,,,,,,验证TLS协议版本兼容性,R,协议版本验证规则,支持的TLS版本范围,1
,,,,,,,,校验加密套件合规性,R,加密套件白名单,允许的加密算法组合,1
,,,,,,,,写入HTTPS通道配置,W,HTTPS通道配置表,TLS版本、加密套件列表、证书信息,1
,,,,,,,,输出配置成功状态,X,配置状态信息,配置结果、错误代码,1
,,,,,,系统管理员在HTTPS通道配置页面点击测试连接按钮,测试HTTPS通道连接,发起HTTPS测试请求,E,测试请求参数,目标地址、测试数据包,1
,,,,,,,,建立TLS加密通道,R,TLS握手协议,协议版本协商、密钥交换,1
,,,,,,,,验证证书有效性,R,证书验证规则,证书颁发机构、有效期、域名匹配,1
,,,,,,,,执行加密数据传输,X,加密数据流,加密后的测试数据包,1
,,,,,,,,输出测试结果,X,测试结果信息,连接状态、错误详情,1
,,,,,发起者：总部平台，接收者：系统管理模块-总部平台对接子系统,总部平台发起HTTPS通道连接请求,处理HTTPS通道连接,接收HTTPS连接请求,E,连接请求信息,客户端IP、协议版本,1
,,,,,,,,协商TLS协议版本,R,协议版本策略,支持的TLS版本列表,1
,,,,,,,,选择加密套件,R,加密套件策略,优先级排序的加密算法组合,1
,,,,,,,,生成并发送服务器证书,X,数字证书,证书内容、签名算法,1
,,,,,,,,建立加密通信通道,X,加密通道状态,会话密钥、加密状态,1
,,总部平台访问凭证配置,支持配置调用集团平台使用的AKSK信息,,发起者：系统管理员，接收者：系统管理-总部一级平台对接模块,系统管理员点击总部平台访问凭证配置菜单,查看总部平台访问凭证配置信息,查询访问凭证配置信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取访问凭证配置信息,R,AKSK信息,访问密钥(AK)、秘密密钥(SK)、描述、状态,1
,,,,,,系统管理员在访问凭证配置页面点击保存按钮，配置新的AKSK信息,配置总部平台访问凭证,输入访问凭证信息,E,AKSK信息,访问密钥(AK)、秘密密钥(SK)、描述、状态,1
,,,,,,,,验证访问凭证格式,X,校验结果,格式校验状态、错误信息,1
,,,,,,,,保存访问凭证信息,W,AKSK信息,访问密钥(AK)、秘密密钥(SK)、描述、状态,1
,,访问凭证查看,展示总部平台访问AK,2.0,发起者：管理员，接收者：系统管理模块-总部一级平台对接模块,管理员点击访问凭证查看按钮,查看总部平台访问凭证信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取访问凭证数据,R,访问凭证信息,AK名称、AK密钥、创建时间、状态,1
,,,,,,,,输出访问凭证列表,X,访问凭证信息,AK名称、AK密钥、创建时间、状态,1
,,AKSK认证,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5.0,发起者：系统管理员，接收者：系统管理模块-总部一级平台对接模块,系统管理员点击AKSK认证配置菜单,配置AKSK认证信息,输入AKSK认证信息,E,AKSK认证信息,AccessKey、SecretKey、密码算法类型,1
,,,,,,,,保存AKSK认证配置,W,AKSK认证配置,AccessKey、SecretKey、密码算法类型、生效时间,1
,,,,,发起者：系统管理模块-总部一级平台对接模块，接收者：总部平台认证服务,系统检测到需要与总部平台进行认证,执行AKSK认证,读取AKSK认证配置,R,AKSK认证配置,AccessKey、SecretKey、密码算法类型,1
,,,,,,,,应用密码算法生成认证签名,X,认证请求数据,签名算法、签名值、时间戳,1
,,,,,,,,发送认证请求至总部平台,X,认证请求报文,认证头信息、签名数据、请求参数,1
,用户认证管理,用户列表查询,列表展示内容：序号、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,2.0,发起者：管理员，接收者：系统管理-用户认证管理模块,管理员点击用户列表查询按钮,查询用户列表信息,输入查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户列表数据,R,用户信息,序号、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,1
,,,,,,,,输出用户列表数据,X,用户信息,序号、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,1
,,用户注册,注册用户信息，录入内容：账户名、姓名、用户类型、备注,3.0,发起者：用户，接收者：系统-用户认证模块,用户点击注册按钮，提交用户注册信息,录入并保存用户注册信息,输入用户注册信息,E,用户注册信息,账户名、姓名、用户类型、备注,1
,,,,,,,,保存用户注册信息,W,用户注册信息,账户名、姓名、用户类型、备注,1
,,用户编辑,编辑用户信息，编辑用户名称,4.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理界面点击编辑按钮，进入用户编辑页面,编辑用户信息,输入用户基本信息,E,用户信息,用户ID、用户名、密码、角色、状态,1
,,,,,,,,验证用户信息有效性,R,用户信息,用户名、密码规则、角色权限,1
,,,,,,,,保存更新后的用户信息,W,用户信息,用户ID、用户名、密码、角色、状态,1
,,,,,,管理员在用户管理界面点击保存按钮，提交用户信息修改,提交用户信息修改,校验用户输入完整性,E,校验规则,必填字段、格式规则,1
,,,,,,,,更新用户认证状态,W,用户认证状态,用户ID、认证状态,1
,,启用,启用禁用的用户,2.0,发起者：管理员，接收者：系统管理-用户认证管理模块,管理员在用户管理页面点击启用按钮，启用禁用的用户,启用禁用用户,查询用户信息,R,用户信息,用户ID、用户名、当前状态,1
,,,,,,,,更新用户状态,W,用户状态信息,用户ID、状态（启用）,1
,,,,,,,,返回启用结果,X,操作结果,操作状态（成功/失败）、提示信息,1
,,禁用,禁用用户，禁止用户登录,,发起者：系统管理员，接收者：用户认证系统,系统管理员在用户管理界面点击禁用用户按钮,禁用用户账户,输入待禁用用户信息,E,用户信息,用户名、用户ID,1
,,,,,,,,读取用户当前状态,R,用户账户信息,用户状态（启用/禁用）,1
,,,,,,,,更新用户状态为禁用,W,用户账户信息,用户状态（禁用）,1
,,,,,,,,返回禁用操作结果,X,操作结果,操作状态（成功/失败）、错误信息,1
,,重置密码,重置用户登录口令为默认口令,,发起者：管理员，接收者：系统-用户认证模块,管理员在用户管理界面点击'重置密码'按钮，选择目标用户并确认操作,重置用户登录密码为默认口令,输入目标用户信息,E,用户信息,用户名、用户ID,1
,,,,,,,,验证管理员操作权限,R,权限信息,管理员权限、用户权限,1
,,,,,,,,生成默认密码,E,密码策略,默认密码规则、密码复杂度,1
,,,,,,,,更新用户密码记录,W,用户凭证,用户ID、加密后的默认密码,1
,,,,,,,,发送密码重置通知,X,通知信息,用户邮箱、重置时间、默认密码,1
,,解锁,解锁到期用户或多次录入错误口令的用户,,发起者：系统管理员，接收者：用户认证管理系统,管理员在用户管理界面点击解锁按钮，选择用户进行解锁,解锁用户账户,验证管理员权限,R,管理员信息,管理员用户名、权限级别,1
,,,,,,,,查询用户锁定状态,R,用户认证数据,用户ID、锁定状态、锁定原因,1
,,,,,,,,执行账户解锁操作,W,用户认证数据,用户ID、锁定状态、解锁时间,1
,,,,,,,记录解锁操作日志,生成解锁操作日志,E,操作日志,操作时间、操作者、用户ID、操作类型,1
,,,,,,,,写入解锁操作日志,W,操作日志,操作时间、操作者、用户ID、操作类型,1
,,设置有效期,设置用户的有效期,3.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击设置有效期按钮,查看用户有效期设置,查询用户基本信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户有效期信息,R,用户有效期数据,用户ID、有效期开始日期、有效期结束日期,1
,,,,,,管理员在用户详情页面点击保存有效期按钮,设置用户有效期,输入有效期参数,E,有效期参数,用户ID、有效期开始日期、有效期结束日期,1
,,,,,,,,验证日期格式有效性,X,验证结果,格式校验状态,1
,,,,,,,,更新用户有效期记录,W,用户有效期数据,用户ID、有效期开始日期、有效期结束日期,1
,,删除,删除用户,2.0,,管理员在用户管理界面点击删除按钮并输入用户ID,删除用户,输入用户ID,E,用户标识信息,用户ID,1
,,,,,,,,读取用户信息,R,用户基本信息,用户ID、用户名、创建时间,1
,,,,,,,,删除用户记录,W,用户基本信息,用户ID,1
,,,,,,,,返回删除结果,X,操作结果,删除状态、错误信息,1
,,列表查询,列表展示内容：序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,3.0,,管理员点击用户认证列表查询菜单,查看用户认证列表信息,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户认证列表数据,R,用户认证信息,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,,,,,,,输出用户认证列表,X,用户认证列表,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,,,,,管理员在用户认证管理页面输入查询条件并点击查询按钮,根据条件查询用户认证列表,输入查询条件,E,查询条件,账号名、姓名、审核状态、申请时间范围,1
,,,,,,,,读取符合条件的用户认证数据,R,用户认证信息,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,,,,,,,输出查询结果,X,用户认证列表,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,删除注册记录,删除注册用户的记录信息,2.0,发起者：管理员，接收者：系统管理模块,管理员在用户管理界面点击删除按钮并输入用户ID,删除注册用户记录,输入用户ID进行删除操作,E,用户删除请求,用户ID,1
,,,,,,,,读取待删除的用户记录,R,用户注册记录,用户ID、用户名、注册时间、认证状态,1
,,,,,,,,执行用户记录删除操作,W,用户注册记录,用户ID,1
,,,,,,,,返回删除操作结果,X,操作反馈,操作状态（成功/失败）、错误信息,1
,,用户注册审核,审核通过/拒绝、所属角色、审批意见,3.0,发起者：管理员，接收者：系统管理模块-用户认证管理,管理员点击用户注册审核菜单,查看待审核的用户注册信息,查询待审核用户列表,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户注册信息,R,用户注册信息,用户名、邮箱、注册时间、所属角色,1
,,,,,,管理员在用户注册审核页面点击审核按钮，选择通过/拒绝并填写审批意见,处理用户注册审核请求,输入审核结果,E,审核结果,审核状态（通过/拒绝）、审批意见,1
,,,,,,,,更新用户审核状态,W,用户注册信息,审核状态、审批意见,1
,,,,,,,,输出审核结果,X,审核结果,审核状态、审批意见,1
,访问控制管理,口令登录,使用用户口令进行登录,,发起者：用户，接收者：系统-访问控制模块,用户在登录页面点击登录按钮并输入用户名和密码,用户登录验证,接收用户输入的登录信息,E,用户输入信息,用户名、密码,1
,,,,,,,,查询数据库中的用户信息,R,用户信息表,用户名、加密密码、账号状态,1
,,,,,,,,验证密码是否匹配,X,密码验证结果,验证状态（成功/失败）,1
,,,,,,,,检查账号状态是否正常,X,账号状态结果,状态（启用/禁用/锁定）,1
,,,,,发起者：系统，接收者：用户,系统完成登录验证,用户登录成功处理,生成会话令牌,W,会话信息,用户ID、令牌值、过期时间,1
,,,,,,,,返回登录成功响应,X,登录结果,状态码（200）、会话令牌,1
,,Ukey登录,使用Ukey智能密钥进行用户登录,5.0,发起者：用户，接收者：系统-访问控制模块,用户插入Ukey并点击登录按钮,验证Ukey登录请求,读取Ukey信息,R,Ukey信息,Ukey序列号、数字证书,1
,,,,,,,,查询用户信息,R,用户信息,用户ID、绑定Ukey序列号,1
,,,,,,,,验证Ukey有效性,E,验证规则,证书有效期、CA签名,1
,,,,,发起者：系统-访问控制模块，接收者：用户,Ukey验证通过,生成登录会话,创建会话令牌,W,会话信息,会话ID、用户ID、过期时间,1
,,,,,,,,返回登录结果,X,响应信息,登录状态、会话令牌,1
,,,,,发起者：用户，接收者：系统-访问控制模块,用户首次绑定Ukey,绑定Ukey到用户账户,输入用户凭证,E,用户凭证,用户名、密码,1
,,,,,,,,读取Ukey证书,R,Ukey信息,公钥、证书序列号,1
,,,,,,,,保存Ukey绑定关系,W,用户绑定信息,用户ID、Ukey序列号、绑定时间,1
,,口令黑名单列表查询,查询禁用的登录口令，禁止用户使用安全级别低的口令进行登录,2.0,发起者：管理员，接收者：访问控制管理系统,管理员点击口令黑名单列表查询菜单,查看口令黑名单列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取口令黑名单数据,R,口令黑名单记录,禁用口令、禁用原因、禁用时间、创建人,1
,,,,,,,,输出分页后的黑名单列表,X,分页结果,禁用口令、禁用原因、禁用时间,1
,,,,,发起者：系统，接收者：访问控制管理系统,用户尝试使用黑名单中的口令登录,校验口令是否在黑名单中,接收登录请求中的口令,E,登录请求,用户名、输入口令,1
,,,,,,,,查询口令是否存在于黑名单,R,口令黑名单记录,禁用口令,1
,,,,,,,,输出校验结果,X,校验结果,是否在黑名单中,1
,,新建口令黑名单,新增用户登录口令黑名单,3.0,发起者：管理员，接收者：系统管理模块-访问控制管理,管理员在口令黑名单管理页面点击添加黑名单口令按钮,添加用户登录口令黑名单,输入黑名单口令,E,黑名单口令信息,口令内容,1
,,,,,,,,读取口令黑名单规则配置,R,黑名单规则配置,最小长度、最大长度、特殊字符要求,1
,,,,,,,,校验口令符合规则,X,校验结果,是否符合规则,1
,,,,,,,,读取现有黑名单口令列表,R,黑名单口令列表,口令内容列表,1
,,,,,,,,校验口令是否已存在,X,校验结果,是否重复,1
,,,,,,,,保存黑名单口令,W,黑名单口令信息,口令内容,1
,,编辑口令黑名单,编辑用户登录口令黑名单,2.0,发起者：系统管理员，接收者：访问控制模块,系统管理员在访问控制管理界面点击编辑口令黑名单按钮,查看当前口令黑名单,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取口令黑名单数据,R,黑名单条目,黑名单ID、口令、创建时间、操作类型（新增/删除）,1
,,,,,,系统管理员在口令黑名单页面点击保存按钮,编辑口令黑名单,输入新增/修改的口令条目,E,黑名单条目,口令、操作类型（新增/删除）,1
,,,,,,,,校验口令格式及唯一性,R,校验规则,最小长度、特殊字符要求、重复性校验规则,1
,,,,,,,,写入更新后的黑名单数据,W,黑名单条目,黑名单ID、口令、操作类型（新增/删除）,1
,,删除口令黑名单,删除用户登录口令黑名单,,发起者：管理员，接收者：系统管理模块-访问控制管理,管理员在访问控制管理页面点击删除按钮，选择用户登录口令黑名单条目并确认删除,删除用户登录口令黑名单条目,输入删除请求,E,删除请求,黑名单条目ID、操作用户ID,1
,,,,,,,,读取黑名单条目信息,R,黑名单条目,黑名单条目ID、口令内容、创建时间、创建用户ID,1
,,,,,,,,执行黑名单条目删除,W,黑名单条目,黑名单条目ID,1
,,,,,,,,输出删除结果,X,操作结果,操作状态（成功/失败）、错误信息,1
,,,,,,管理员在访问控制管理页面点击查询按钮，查看用户登录口令黑名单条目,查询用户登录口令黑名单条目,输入查询条件,E,查询条件,分页信息（页码、单页数量）、口令内容关键字,1
,,,,,,,,读取黑名单条目信息,R,黑名单条目列表,黑名单条目ID、口令内容、创建时间、创建用户ID,1
,,,,,,,,输出查询结果,X,黑名单条目列表,黑名单条目ID、口令内容、创建时间、创建用户ID,1
,,智能密码钥匙列表,列表展示内容：序列号、类型、所属账号名、所属用户名,4.0,发起者：管理员，接收者：系统管理-访问控制管理模块,管理员点击智能密码钥匙列表菜单,查看智能密码钥匙列表,查询智能密码钥匙列表信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取智能密码钥匙列表数据,R,智能密码钥匙列表信息,序列号、类型、所属账号名、所属用户名,1
,,智能密码钥匙新增,选择智能密钥钥匙类型，录入Ukey口令，选择绑定的用户信息,,发起者：管理员，接收者：系统管理模块-访问控制管理,管理员在智能密码钥匙管理页面点击新增按钮,选择智能密码钥匙类型,输入智能密码钥匙类型查询条件,E,查询条件,钥匙类型名称、部门,1
,,,,,,,,读取可用钥匙类型列表,R,钥匙类型列表,类型名称、描述、加密算法,1
,,,,,,,,选择指定钥匙类型,E,钥匙类型信息,类型名称、加密算法,1
,,,,,,,,保存钥匙类型选择结果,W,钥匙类型配置,类型名称、加密算法、选择状态,1
,,,,,,管理员在智能密码钥匙配置页面输入Ukey口令,录入Ukey口令,输入Ukey口令及确认口令,E,Ukey口令信息,口令、确认口令,1
,,,,,,,,验证口令一致性,X,验证结果,是否一致、错误信息,1
,,,,,,,,保存Ukey口令配置,W,Ukey口令配置,加密后的口令、创建时间,1
,,,,,,管理员在智能密码钥匙绑定页面选择用户,绑定用户信息,输入用户查询条件,E,用户查询条件,用户名、部门、状态,1
,,,,,,,,读取符合条件的用户列表,R,用户列表,用户ID、用户名、部门、状态,1
,,,,,,,,选择目标用户进行绑定,E,用户绑定信息,用户ID、用户名、选择状态,1
,,,,,,,,保存用户与钥匙的绑定关系,W,用户钥匙绑定,用户ID、钥匙序列号、绑定时间,1
,,智能密码钥匙启用,启用禁用的UKey智能密码钥匙,,发起者：管理员，接收者：系统管理-访问控制模块,管理员在访问控制管理页面点击启用按钮，启用禁用的UKey智能密码钥匙,启用禁用的UKey智能密码钥匙,输入UKey启用信息,E,UKey启用信息,UKey编号、启用原因、操作人,1
,,,,,,,,验证UKey状态,R,UKey状态信息,UKey编号、当前状态、所属用户,1
,,,,,,,,更新UKey状态为启用,W,UKey状态信息,UKey编号、新状态、更新时间,1
,,,,,,,,输出启用结果,X,操作结果,操作状态、错误信息,1
,,,,,,管理员在访问控制管理页面点击查询按钮，查看禁用的UKey列表,查询禁用的UKey列表,输入查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取禁用UKey数据,R,UKey列表,UKey编号、状态、所属用户、禁用时间,1
,,,,,,,,输出查询结果,X,UKey列表,UKey编号、状态、所属用户、禁用时间,1
,,智能密码钥匙禁用,禁用Ukey智能密码钥匙，禁止使用该Ukey进行登录,,,管理员在访问控制管理页面点击禁用Ukey按钮,禁用智能密码钥匙,输入Ukey序列号,E,Ukey基本信息,序列号,1
,,,,,,,,验证Ukey存在性,R,Ukey注册信息,序列号、注册状态,1
,,,,,,,,更新Ukey状态为禁用,W,Ukey状态信息,序列号、状态（禁用）,1
,,,,,,,,输出禁用结果,X,操作反馈,操作结果（成功/失败原因）,1
,,,,,发起者：系统，接收者：认证服务模块,Ukey状态更新为禁用后触发认证流程,阻止禁用Ukey登录,读取Ukey认证请求,R,认证请求信息,Ukey序列号、认证时间,1
,,,,,,,,查询Ukey状态,R,Ukey状态信息,序列号、状态,1
,,,,,,,,输出认证拒绝,X,认证响应,拒绝原因（Ukey已禁用）,1
,,智能密码钥匙删除,删除Ukey智能密码钥匙凭证,,发起者：管理员，接收者：系统管理模块-访问控制管理,管理员在访问控制管理界面点击删除Ukey按钮并确认操作,删除Ukey智能密码钥匙凭证,查询待删除的Ukey信息,E,查询条件,Ukey编号、用户标识,1
,,,,,,,,读取Ukey详细信息,R,Ukey信息,Ukey编号、绑定用户、状态、创建时间,1
,,,,,,,,验证删除权限,R,权限信息,管理员权限等级、操作权限,1
,,,,,,,,执行Ukey删除操作,W,Ukey状态变更,Ukey编号、新状态（已删除）,1
,,,,,,,,记录删除日志,W,操作日志,操作类型、操作时间、操作者、Ukey编号,1
,,,,,,,,输出删除结果,X,操作反馈,删除状态（成功/失败）、错误代码,1
,,,,,发起者：系统，接收者：审计模块,Ukey删除操作完成,触发审计日志记录,获取删除事件数据,R,Ukey删除事件,Ukey编号、操作者、操作时间,1
,,,,,,,,写入审计日志,W,审计记录,事件类型、事件详情、时间戳,1
,,是否开启口令登录,是否开启口令登录，允许用户使用口令进行登录,,发起者：系统管理员，接收者：系统管理模块-访问控制管理,系统管理员在访问控制管理页面点击开启口令登录配置按钮,开启口令登录,输入开启口令登录配置,E,口令登录配置,开启状态、生效时间,1
,,,,,,,,验证口令登录配置,R,系统配置规则,配置校验规则、权限校验,1
,,,,,,,,保存口令登录配置,W,系统配置表,口令登录开关、更新时间,1
,,,,,,,,输出配置结果,X,操作反馈,配置状态、错误信息,1
,,,,,,系统管理员在访问控制管理页面点击关闭口令登录配置按钮,关闭口令登录,输入关闭口令登录配置,E,口令登录配置,关闭状态、生效时间,1
,,,,,,,,验证关闭操作权限,R,用户权限表,管理员权限等级,1
,,,,,,,,更新系统配置表,W,系统配置表,口令登录开关、更新时间,1
,,,,,,,,返回操作结果,X,操作反馈,配置状态、错误信息,1
,,,,,,系统管理员在访问控制管理页面点击查看当前口令登录状态,查询口令登录状态,读取系统配置表,R,系统配置表,口令登录开关、最后更新时间,1
,,,,,,,,输出当前配置状态,X,配置状态信息,开关状态、生效时间,1
,,是否开启UKey登录,是否开启UKey登录，允许用户使用Ukey进行登录,2.0,,系统管理员在访问控制设置页面点击开启UKey登录按钮,开启UKey登录功能,输入开启UKey登录配置,E,UKey登录配置,启用状态（开启）,1
,,,,,,,,验证UKey登录配置,R,系统配置校验规则,配置项合法性校验规则,1
,,,,,,,,保存UKey登录配置,W,系统访问控制配置,UKey登录启用状态,1
,,,,,,,,返回配置成功提示,X,操作反馈信息,操作结果（成功/失败）,1
,,,,,,系统管理员在访问控制设置页面点击关闭UKey登录按钮,关闭UKey登录功能,输入关闭UKey登录配置,E,UKey登录配置,启用状态（关闭）,1
,,,,,,,,验证UKey登录配置,R,系统配置校验规则,配置项合法性校验规则,1
,,,,,,,,保存UKey登录配置,W,系统访问控制配置,UKey登录启用状态,1
,,,,,,,,返回配置成功提示,X,操作反馈信息,操作结果（成功/失败）,1
,,默认口令,默认口令，设置用户注册后默认口令信息,,,系统管理员进入默认口令配置页面,查看默认口令配置信息,查询默认口令配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取默认口令配置数据,R,默认口令配置,默认口令值、有效期、是否启用,1
,,,,,,系统管理员在默认口令配置页面点击保存按钮,配置默认口令信息,输入默认口令参数,E,默认口令配置,默认口令值、有效期、是否启用,1
,,,,,,,,验证输入有效性,X,校验结果,校验状态、错误信息,1
,,,,,,,,保存默认口令配置,W,默认口令配置,默认口令值、有效期、是否启用,1
,,历史口令限制次数,历史口令限制次数，限制修改口令时，和向上几次历史口令不同,,发起者：用户，接收者：系统管理模块-访问控制管理,用户提交新密码修改请求,验证新密码与历史记录差异,接收用户输入的新密码,E,密码修改请求,用户ID、新密码,1
,,,,,,,,读取用户历史密码记录,R,历史密码列表,用户ID、历史密码集合,1
,,,,,,,,比对新密码与历史密码,X,密码验证结果,是否重复、重复次数,1
,,,,,发起者：系统管理模块，接收者：访问控制管理模块,验证通过后需要更新密码历史记录,更新用户历史密码记录,生成新的历史密码记录,E,历史密码更新,用户ID、新密码、历史记录保留次数,1
,,,,,,,,写入更新后的历史密码记录,W,历史密码列表,用户ID、更新后的密码集合,1
,,长时间未登录禁用账户天数,长时间未登录禁用账户天数，设置用户多长时间不登录后，自动锁定用户,,发起者：管理员，接收者：系统管理模块-访问控制管理,管理员在访问控制配置页面点击保存按钮，设置长时间未登录禁用账户天数,配置禁用账户天数,输入禁用账户天数,E,配置参数,禁用天数,1
,,,,,,,,验证输入天数有效性,X,校验结果,是否合法,1
,,,,,,,,保存禁用天数配置,W,系统配置信息,禁用天数,1
,,,,,发起者：系统，接收者：访问控制模块,系统检测到用户登录状态变化,执行账户禁用逻辑,查询用户最后登录时间,R,用户登录记录,用户ID、最后登录时间,1
,,,,,,,,计算登录间隔天数,X,时间计算结果,间隔天数,1
,,,,,,,,比对禁用天数阈值,R,系统配置信息,禁用天数,1
,,,,,,,,执行账户禁用操作,W,用户状态信息,用户ID、账户状态,1
,,,,,发起者：管理员，接收者：系统管理模块-访问控制管理,管理员在访问控制配置页面查看禁用账户天数配置,查看禁用账户天数配置,查询禁用天数配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取禁用天数配置,R,系统配置信息,禁用天数,1
,,口令有效期天数,设置用户口令有效期天数,,发起者：系统管理员，接收者：系统管理模块-访问控制管理模块,系统管理员在访问控制管理页面点击设置口令有效期天数按钮,查看当前口令有效期天数设置,查询口令有效期天数配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取当前口令有效期天数,R,口令有效期配置,当前有效期天数,1
,,,,,,系统管理员在口令有效期配置页面输入新的有效期天数并提交,设置新的口令有效期天数,输入新的口令有效期天数,E,有效期配置,新有效期天数,1
,,,,,,,,验证输入的有效性,R,校验规则,最小天数、最大天数,1
,,,,,,,,保存更新后的口令有效期配置,W,口令有效期配置,新有效期天数,1
,,,,,,,,输出配置更新结果,X,操作结果,成功/失败状态、错误信息,1
,,口令有效期告警天数,口令有效期告警天数，口令即将到期告警天数,,发起者：管理员，接收者：系统管理模块-访问控制管理子系统,管理员在访问控制管理界面点击口令有效期告警配置菜单,查看口令有效期告警天数配置,输入查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取口令有效期告警天数配置,R,告警配置信息,当前告警天数、配置时间、配置人,1
,,,,,,,,输出查询结果,X,告警配置信息,告警天数列表、分页信息,1
,,,,,,管理员在告警配置页面点击修改告警天数按钮并输入新值,修改口令有效期告警天数配置,输入新告警天数,E,告警配置参数,告警天数、生效范围,1
,,,,,,,,验证输入有效性,R,系统规则,最小天数限制、最大天数限制,1
,,,,,,,,保存告警天数配置,W,告警配置信息,告警天数、配置时间、配置人,1
,,,,,,,,输出保存结果,X,操作反馈,保存状态、错误信息,1
,,登录失败次数限制次数,登录失败次数限制次数，登录口令允许错误次数，超过锁定对应用户,,发起者：系统管理员，接收者：系统管理模块-访问控制子系统,系统管理员在访问控制配置界面设置登录失败次数限制参数,配置登录失败次数限制规则,输入登录失败次数限制参数,E,失败次数规则配置,最大允许失败次数、锁定持续时间(分钟),1
,,,,,,,,验证配置参数有效性,X,配置校验结果,参数合法性状态、错误信息,1
,,,,,,,,保存失败次数限制配置,W,系统安全配置表,规则ID、最大失败次数、锁定时间、生效状态,1
,,,,,发起者：用户，接收者：认证服务模块,用户连续输入错误密码触发失败计数,处理登录失败事件,记录单次登录失败事件,W,用户登录记录表,用户名、失败时间、失败次数,1
,,,,,,,,更新用户失败计数器,W,用户状态表,用户名、累计失败次数、最后一次失败时间,1
,,,,,,,,检查失败次数是否超过阈值,R,系统安全配置表,当前失败次数、最大允许失败次数,1
,,,,,,,,触发账户锁定流程,X,安全事件通知,用户名、锁定原因、触发时间,1
,,,,,发起者：系统，接收者：账户管理模块,检测到用户登录失败次数超过限制,执行账户锁定操作,更新用户账户锁定状态,W,用户账户表,用户名、锁定状态、锁定开始时间,1
,,,,,,,,生成账户锁定审计日志,W,安全审计日志表,用户名、操作类型、触发时间、关联IP,1
,,,,,,,,发送账户锁定通知,X,系统通知消息,接收人、通知内容、通知类型,1
,,登录失败锁定时长(分钟),登录失败锁定时长(分钟)，,,发起者：管理员，接收者：系统管理模块-访问控制管理子系统,管理员在访问控制管理页面点击登录失败锁定时长配置菜单,查看登录失败锁定时长配置,查询登录失败锁定时长配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取登录失败锁定时长配置,R,锁定时长配置,当前锁定时长(分钟)、配置更新时间,1
,,,,,,管理员在登录失败锁定时长配置页面输入新的锁定时长并点击保存,修改登录失败锁定时长配置,输入新的锁定时长参数,E,锁定时长参数,目标锁定时长(分钟)、参数校验规则,1
,,,,,,,,验证输入参数有效性,R,系统校验规则,最小值、最大值、整数格式,1
,,,,,,,,更新锁定时长配置,W,锁定时长配置,原锁定时长、新锁定时长、更新时间戳,1
,,,,,,,,返回配置更新结果,X,操作反馈,更新成功/失败状态、错误信息,1
,,是否强制修改默认口令,是否强制修改默认口令,,发起者：管理员，接收者：系统管理模块,管理员在访问控制配置页面点击查看强制修改默认口令配置,查看是否强制修改默认口令配置,查询强制修改默认口令配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取强制修改默认口令配置,R,系统配置信息,配置名称、配置值,1
,,,,,,管理员在访问控制配置页面点击保存强制修改默认口令配置,修改是否强制修改默认口令配置,输入强制修改默认口令配置,E,系统配置信息,配置名称、配置值,1
,,,,,,,,验证配置值有效性,X,验证结果,是否合法,1
,,,,,,,,保存强制修改默认口令配置,W,系统配置信息,配置名称、配置值,1
,上报周期管理,上报内容列表,列表展示上报内容、上报周期、是否启用、上报周期、最后上报时间,4.0,发起者：管理员，接收者：系统管理-上报周期管理模块,管理员点击上报内容列表菜单,查看上报内容列表信息,查询上报内容列表,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报内容列表信息,R,上报内容列表信息,上报内容名称、上报周期、是否启用、最后上报时间,1
,,,,,,管理员在上报内容列表页面进行分页操作,分页查看上报内容列表信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页后的上报内容列表,R,上报内容列表信息,上报内容名称、上报周期、是否启用、最后上报时间,1
,,上报内容配置,"提供上报内容配置选项,可以控制对应上报项目是否开启上报",,发起者：管理员，接收者：系统管理模块-上报周期管理,管理员点击上报内容配置菜单,查看上报内容配置信息,查询上报内容配置信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报内容配置信息,R,上报内容配置信息,上报项目名称、是否开启上报、上报周期类型,1
,,,,,,管理员在上报内容配置页面点击开启/关闭按钮,修改上报内容配置状态,选择上报项目,E,上报项目信息,上报项目名称,1
,,,,,,,,输入上报状态,E,上报状态信息,是否开启上报,1
,,,,,,,,保存上报内容配置状态,W,上报内容配置信息,上报项目名称、是否开启上报,1
,,上报频率配置,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7.0,发起者：管理员，接收者：系统管理-上报周期管理模块,管理员点击上报频率配置菜单,查看上报频率配置信息,查询上报频率配置信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报频率配置信息,R,上报频率配置信息,上报项目名称、上报频率值、频率单位（分钟/小时/天）,1
,,,,,,管理员在上报频率配置页面点击保存按钮，配置新的上报频率,配置上报频率,选择上报项目,E,上报项目信息,上报项目名称、项目标识符,1
,,,,,,,,输入上报频率参数,E,频率配置参数,频率值、频率单位（分钟/小时/天）,1
,,,,,,,,保存上报频率配置,W,上报频率配置信息,上报项目名称、频率值、频率单位,1
,日志管理/统计分析,登录日志查询,记录平台所有登录日志，包含成功和异常,4.0,发起者：系统管理员，接收者：日志管理系统,管理员在日志管理页面点击登录日志查询按钮,查询登录日志,输入查询条件,E,查询参数,时间范围、用户ID、登录状态（成功/异常）,1
,,,,,,,,读取登录日志数据,R,登录日志记录,用户ID、登录时间、IP地址、登录状态、失败原因（异常时）,1
,,,,,,,,展示查询结果,X,日志列表,用户ID、登录时间、IP地址、登录状态、失败原因,1
,,,,,,管理员在登录日志查询结果页面点击导出按钮,导出登录日志,选择导出格式和范围,E,导出参数,导出格式（CSV/Excel）、时间范围,1
,,,,,,,,生成导出文件,W,日志文件,用户ID、登录时间、IP地址、登录状态、失败原因,1
,,,,,,,,下载导出文件,X,下载链接,文件名称、下载地址,1
,,,,,,管理员在登录日志列表中点击某条日志的详情按钮,查看登录日志详情,选择日志条目,E,日志标识,日志ID,1
,,,,,,,,读取详细日志信息,R,详细日志记录,用户ID、登录时间、IP地址、登录状态、失败原因、请求参数、响应信息,1
,,,,,,,,展示详细信息,X,日志详情,用户ID、登录时间、IP地址、登录状态、失败原因、请求参数、响应信息,1
,,批量审计,批量审批登录日志,,发起者：管理员，接收者：日志管理系统,管理员在日志管理页面选择多个登录日志并点击批量审批按钮,查看待审批的登录日志,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取待审批登录日志列表,R,登录日志信息,日志ID、用户账号、登录时间、登录状态、审批状态,1
,,,,,,,执行批量审批操作,选择待审批日志条目,E,日志选择信息,日志ID列表、审批操作类型（通过/驳回）,1
,,,,,,,,确认批量审批操作,E,审批确认信息,审批原因、操作人ID,1
,,,,,,,,更新日志审批状态,W,日志审批记录,日志ID、审批状态、审批时间、审批人ID,1
,,日志导出,导出登录日志,,发起者：管理员，接收者：系统管理-日志管理模块,管理员在日志导出页面点击导出登录日志按钮,查询登录日志以导出,输入日志查询条件,E,日志查询条件,时间范围、用户ID、登录状态,1
,,,,,,,,读取符合查询条件的登录日志,R,登录日志数据,登录时间、用户信息、IP地址、登录状态,1
,,,,,,,,生成日志导出文件,X,导出文件,文件格式、文件内容,1
,,,,,,管理员在日志导出页面点击执行导出操作,执行日志导出,选择导出文件格式,E,导出格式,CSV、Excel,1
,,,,,,,,生成并下载导出文件,X,导出文件,文件名、文件路径,1
,,,,,,,,记录导出操作日志,W,系统操作日志,操作人、操作时间、操作类型,1
,,操作日志查询,记录平台和租户的所有操作日志，包含各类密码服务的配置,,发起者：系统管理员，接收者：日志管理系统,系统管理员在日志管理页面点击操作日志查询按钮,查询操作日志,输入查询条件,E,查询条件,时间范围、操作用户、操作类型、租户标识,1
,,,,,,,,读取操作日志数据,R,操作日志,操作时间、操作用户、操作类型、操作详情、租户标识、密码服务配置信息,1
,,,,,,,,展示操作日志列表,X,日志列表,操作时间、操作用户、操作类型、租户标识,1
,,,,,,系统管理员在操作日志列表点击导出按钮,导出操作日志,选择导出格式,E,导出参数,文件格式（CSV/Excel）,1
,,,,,,,,生成导出文件,W,操作日志文件,操作时间、操作用户、操作类型、操作详情、租户标识、密码服务配置信息,1
,,,,,,,,下载导出文件,X,下载文件,文件名称、文件大小,1
,,,,,,系统管理员在操作日志列表点击查看详情按钮,查看操作日志详情,选择日志条目,E,日志标识,日志ID,1
,,,,,,,,读取详细日志信息,R,操作日志详情,操作时间、操作用户、操作类型、操作详情、租户标识、密码服务配置信息、请求参数、响应结果,1
,,,,,,,,展示日志详情,X,日志详情,操作时间、操作用户、操作类型、操作详情、租户标识、密码服务配置信息、请求参数、响应结果,1
,,批量审批,批量审批操作日志,,发起者：管理员，接收者：系统管理模块-日志管理子系统,管理员在操作日志管理页面点击批量审批按钮,查看待审批操作日志列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取待审批日志列表,R,操作日志信息,日志ID、操作类型、操作时间、操作人、审批状态,1
,,,,,,管理员在待审批日志列表中选择多个日志并点击批量审批,执行批量审批操作,选择待审批日志,E,日志ID列表,"日志ID1, 日志ID2, 日志ID3...",1
,,,,,,,,输入审批信息,E,审批信息,审批人、审批意见、审批状态,1
,,,,,,,,更新日志审批状态,W,操作日志信息,日志ID、审批状态,1
,,,,,,管理员在批量审批完成后请求查看审批结果,获取批量审批结果,查询审批结果,E,查询条件,审批时间范围、审批人,1
,,,,,,,,读取审批结果数据,R,审批记录,日志ID、审批人、审批时间、审批状态,1
,,日志导出,导出操作日志,,发起者：用户（管理员），接收者：系统管理模块-日志导出功能,用户在日志管理页面点击导出操作日志按钮,导出操作日志,输入导出条件,E,导出条件,开始时间、结束时间、日志级别、操作类型,1
,,,,,,,,读取操作日志数据,R,操作日志记录,操作时间、操作用户、操作类型、操作详情、IP地址,1
,,,,,,,,生成导出文件,W,导出文件,文件格式（CSV/Excel）、文件内容（操作日志数据）,1
,,,,,,,,输出导出文件,X,导出文件,文件名称、文件大小、下载链接,1
密码应用数据管理,密码应用类型管理,密码应用类型分页列表查询,分页展示平台支持的密码应用类型，列表内容：序号、类型编码、类型名称、备注、创建时间、操作,3.0,发起者：管理员，接收者：密码应用数据管理模块,管理员点击密码应用类型分页列表查询菜单,分页查询密码应用类型列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页密码应用类型数据,R,密码应用类型信息,类型编码、类型名称、备注、创建时间,1
,,,,,,,,输出分页密码应用类型列表,X,密码应用类型列表,类型编码、类型名称、备注、创建时间,1
,,密码应用类型过滤查询,支持根据类型名称和编码进行过滤查询,,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用类型管理页面点击过滤查询按钮,查看过滤后的密码应用类型信息,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取过滤后的密码应用类型信息,R,密码应用类型信息,类型名称、类型编码、描述、创建时间,1
,,,,,,管理员在密码应用类型管理页面输入类型名称和编码进行过滤查询,执行密码应用类型过滤查询,输入过滤条件,E,过滤条件,类型名称、类型编码,1
,,,,,,,,写入过滤条件到查询参数,W,查询参数,类型名称、类型编码,1
,,,,,,,,读取过滤查询结果,R,密码应用类型信息,类型名称、类型编码、描述、创建时间,1
,,,,,,,,输出过滤查询结果,X,查询结果,类型名称、类型编码、描述、创建时间,1
,,新增密码应用类型,新增密码应用类型，录入类型编码、类型名称、备注,4.0,,管理员在密码应用类型管理页面点击新增密码应用类型按钮，录入类型编码、类型名称、备注,新增密码应用类型信息,输入密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1
,,,,,,,,验证密码应用类型编码唯一性,R,现有密码应用类型信息,类型编码,1
,,,,,,,,保存密码应用类型信息,W,密码应用类型信息,类型编码、类型名称、备注,1
,,编辑密码应用类型,编辑密码应用类型，编辑类型编码、类型名称、备注,3.0,发起者：管理员，接收者：密码应用类型管理模块,管理员在密码应用类型管理页面点击编辑按钮，进入编辑密码应用类型页面,编辑密码应用类型信息,读取待编辑的密码应用类型信息,R,密码应用类型信息,类型编码、类型名称、备注,1
,,,,,,,,输入修改后的密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1
,,,,,,,,保存修改后的密码应用类型信息,W,密码应用类型信息,类型编码、类型名称、备注,1
,,删除密码应用类型,应用类型下无对应用时，允许删除密码应用类型,4.0,发起者：管理员，接收者：密码应用数据管理系统-密码应用类型管理模块,管理员在密码应用类型管理页面点击删除按钮,删除密码应用类型,输入密码应用类型信息,E,密码应用类型信息,类型ID、类型名称,1
,,,,,,,,验证密码应用类型下是否存在密码应用,R,密码应用信息,类型ID,1
,,,,,,,,删除密码应用类型,W,密码应用类型信息,类型ID,1
,,,,,,,,输出删除结果,X,操作结果,成功/失败状态、错误信息,1
,,密码应用类型下拉选择,创建应用或过滤查询时，获取类型名称和ID,3.0,发起者：用户，接收者：密码应用数据管理系统-类型管理模块,用户在创建应用或过滤查询时触发类型下拉选择请求,获取密码应用类型下拉选项数据,接收类型下拉选择请求,E,请求参数,请求来源（创建应用/过滤查询）,1
,,,,,,,,读取密码应用类型基础数据,R,密码应用类型信息,类型ID、类型名称、状态、创建时间,1
,,,,,,,,返回类型下拉选项数据,X,类型下拉数据,类型ID、类型名称,1
,,密码应用类型应用数量分布,统计展示平台中应用类型下包含的应用数量分布,4.0,发起者：管理员，接收者：密码应用数据管理模块,管理员访问密码应用类型数量分布统计页面,查询密码应用类型数量分布,输入查询条件,E,查询条件,应用类型名称,1
,,,,,,,,读取应用类型数据,R,应用类型数据,应用类型ID、应用类型名称,1
,,,,,,,,读取应用数据,R,应用数据,应用ID、应用类型ID,1
,,,,,,,,计算类型应用数量,X,统计结果,应用类型名称、应用数量,1
,,,,,发起者：密码应用数据管理模块，接收者：前端展示模块,统计结果生成完成,展示密码应用类型数量分布,输出统计图表数据,X,图表数据,应用类型名称、应用数量、图表类型,1
,,,,,,,,输出分页信息,X,分页信息,总记录数、当前页码、单页数量,1
,密码应用管理,密码应用分页列表查询,分页展示密码应用分页列表信息，列表内容：应用标识、应用名称、所属单位(机构；单租户显示)、业务描述、完整性校验,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击密码应用分页列表查询按钮,分页查询密码应用列表信息,输入分页参数,E,分页信息,页码、每页数量,1
,,,,,,,,读取密码应用列表数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述、完整性校验,1
,,,,,,,,输出分页后的密码应用列表,X,密码应用信息,应用标识、应用名称、所属单位、业务描述、完整性校验,1
,,密码应用过滤查询,支持应用标识、应用名称、简称进行过滤查询,,,管理员在密码应用管理界面点击过滤查询按钮,查看过滤后的密码应用信息,输入过滤条件,E,过滤条件,应用标识、应用名称、简称,1
,,,,,,,,读取分页信息,R,分页信息,页码、单页数量,1
,,,,,,,,查询密码应用数据,R,密码应用数据,应用标识、应用名称、简称、创建时间、状态,1
,,,,,,,,输出过滤结果,X,过滤结果,应用标识、应用名称、简称、创建时间、状态,1
,,,,,,管理员在过滤查询界面输入应用标识/名称/简称进行搜索,执行密码应用过滤查询,接收过滤参数,E,过滤参数,应用标识、应用名称、简称,1
,,,,,,,,验证过滤参数格式,R,参数校验规则,字段类型、长度限制、必填项,1
,,,,,,,,构建数据库查询语句,W,SQL语句,WHERE条件、JOIN语句、ORDER BY排序,1
,,,,,,,,执行数据库查询,R,数据库记录,应用标识、应用名称、简称、创建时间、状态,1
,,,,,,,,返回查询结果集,X,查询结果,应用标识、应用名称、简称、创建时间、状态,1
,,新增密码应用,新增应用，录入内容：应用标识、应用名称、所属单位、业务类型（下拉多选）、业务类型对应的密码服务集群、认证方式（口令、AK/SK）、业务描述；,6.0,,管理员在密码应用管理页面点击新增应用按钮,创建密码应用,输入应用基本信息,E,应用基本信息,应用标识、应用名称、所属单位,1
,,,,,,,,读取业务类型列表,R,业务类型列表,业务类型名称、对应密码服务集群,1
,,,,,,,,选择业务类型及对应集群,E,业务类型配置,选中的业务类型名称、关联的密码服务集群,1
,,,,,,,,读取认证方式选项,R,认证方式列表,口令、AK/SK,1
,,,,,,,,选择认证方式,E,认证方式配置,选中的认证方式,1
,,,,,,,,输入业务描述,E,业务描述信息,业务描述文本,1
,,,,,,,,验证输入数据完整性,R,校验规则,必填字段规则、格式校验规则,1
,,,,,,,,保存密码应用信息,W,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
,,,,,,,,输出操作结果,X,操作反馈,操作状态（成功/失败）、错误信息,1
,,编辑密码应用,可编辑内容：应用名称、所属单位、业务描述,3.0,,管理员点击编辑密码应用按钮,读取密码应用信息以进行编辑,查询密码应用基础信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用详细信息,R,密码应用信息,应用名称、所属单位、业务描述,1
,,,,,,管理员在编辑页面点击保存按钮,保存编辑后的密码应用信息,输入修改后的密码应用信息,E,密码应用信息,应用名称、所属单位、业务描述,1
,,,,,,,,校验输入数据有效性,X,校验结果,校验状态、错误信息,1
,,,,,,,,更新密码应用信息,W,密码应用信息,应用名称、所属单位、业务描述,1
,,删除密码应用,当应用下无密钥、无证书后，删除应用信息，同步删除应用认证方式和密码服务的调度关系等,,,管理员在密码应用管理页面点击删除按钮，确认删除无密钥和证书的应用,删除密码应用及其关联数据,验证应用下无密钥和证书,R,密钥信息,应用ID,1
,,,,,,,,验证应用下无证书,R,证书信息,应用ID,1
,,,,,,,,读取待删除的应用信息,R,应用信息,应用ID、应用名称、创建时间,1
,,,,,,,,删除应用信息,W,应用信息,应用ID,1
,,,,,,,,读取应用认证方式配置,R,应用认证方式,应用ID、认证类型、配置参数,1
,,,,,,,,删除应用认证方式,W,应用认证方式,应用ID,1
,,,,,,,,读取密码服务调度关系,R,密码服务调度关系,应用ID、服务名称、调度策略,1
,,,,,,,,删除密码服务调度关系,W,密码服务调度关系,应用ID,1
,,密码应用详情,展示密码应用的详细信息,4.0,发起者：用户，接收者：密码应用管理模块,用户在密码应用列表中点击某个密码应用的详情按钮,查看密码应用详细信息,输入密码应用标识信息,E,查询条件,密码应用ID,1
,,,,,,,,读取密码应用基础信息,R,密码应用基础数据,应用名称、应用编号、创建时间、所属部门,1
,,,,,,,,读取密码应用配置详情,R,密码应用配置数据,算法类型、密钥长度、证书信息、安全策略,1
,,,,,,,,输出密码应用详细信息,X,密码应用详情展示数据,应用名称、应用编号、创建时间、所属部门、算法类型、密钥长度、证书信息、安全策略,1
,,密码应用信息完整性校验,保障密码应用的数据完整性，如数据被篡改，显示异常,3.0,发起者：系统管理员，接收者：密码应用数据管理系统,系统管理员手动触发密码应用数据完整性校验,执行密码应用数据完整性校验,读取密码应用原始数据及校验值,R,密码应用数据,数据内容、原始校验值,1
,,,,,,,,计算当前数据校验值,X,计算校验值,算法类型、计算结果,1
,,,,,,,,比较原始校验值与计算值,R,校验结果,是否一致、异常类型,1
,,,,,,,,记录校验结果,W,校验日志,校验时间、数据标识、结果状态,1
,,,,,,,,输出异常提示信息,X,异常信息,异常类型、数据标识、建议处理方式,1
,,,,,发起者：系统定时任务，接收者：密码应用数据管理系统,系统定时触发密码应用数据完整性校验,自动执行密码应用数据完整性校验,读取密码应用原始数据及校验值,R,密码应用数据,数据内容、原始校验值,1
,,,,,,,,计算当前数据校验值,X,计算校验值,算法类型、计算结果,1
,,,,,,,,比较原始校验值与计算值,R,校验结果,是否一致、异常类型,1
,,,,,,,,记录校验结果,W,校验日志,校验时间、数据标识、结果状态,1
,,,,,,,,输出异常提示信息,X,异常信息,异常类型、数据标识、建议处理方式,1
,,应用认证凭证列表查询,展示密钥应用调用密码业务接口时使用的所有认证凭证列表,,发起者：管理员，接收者：密码应用管理系统,管理员点击应用认证凭证列表查询菜单,展示认证凭证列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取认证凭证数据,R,认证凭证列表,凭证名称、凭证类型、创建时间、有效期、关联业务系统,1
,,,,,,,,输出认证凭证列表,X,认证凭证列表,凭证名称、凭证类型、创建时间、有效期、关联业务系统,1
,,应用认证凭证过滤查询,支持认证凭证密钥ID和描述过滤查询,,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证管理页面输入密钥ID和描述过滤条件并点击查询按钮,执行认证凭证过滤查询,输入过滤条件,E,过滤条件,密钥ID、描述,1
,,,,,,,,读取认证凭证数据,R,认证凭证信息,密钥ID、描述、创建时间、状态,1
,,,,,,,,输出过滤结果,X,过滤结果列表,密钥ID、描述、创建时间、状态,1
,,新增应用认证凭证,选择认证方式，AK/SK ；创建成功后，自动下载对应SK文件；同步到认证中心，支持凭证的认证授权,5.0,发起者：管理员，接收者：密码应用管理系统,管理员在应用创建页面选择AK/SK认证方式并提交,创建AK/SK认证凭证,选择认证方式,E,认证方式配置,认证类型(AK/SK),1
,,,,,,,,生成AK/SK密钥对,W,认证凭证信息,AK、SK、有效期、创建时间,1
,,,,,,,,读取认证方式配置,R,认证方式配置,认证类型(AK/SK),1
,,,,,发起者：系统，接收者：密码应用管理系统,系统检测到AK/SK凭证创建成功,生成并下载SK文件,生成SK文件内容,W,SK文件数据,SK值、加密算法、文件格式,1
,,,,,,,,输出SK文件,X,文件下载响应,文件流、文件名、MIME类型,1
,,,,,发起者：密码应用管理系统，接收者：认证中心,系统需要同步AK/SK凭证到认证中心,同步认证凭证到认证中心,调用认证中心接口,X,认证中心接口请求,AK、SK、应用ID、同步时间,1
,,,,,,,,接收认证中心响应,R,认证中心接口响应,同步状态、错误代码,1
,,,,,发起者：认证中心，接收者：密码应用管理系统,认证中心需要验证AK/SK凭证,执行认证授权,接收认证请求,E,认证请求参数,AK、请求时间戳、签名,1
,,,,,,,,验证签名有效性,R,认证凭证信息,SK、加密算法、有效期,1
,,,,,,,,返回认证结果,X,认证响应,认证状态、过期时间、错误信息,1
,,编辑应用认证凭证,编辑认证凭证的描述信息,4.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证管理页面点击编辑按钮，进入编辑认证凭证描述信息的流程,查看当前认证凭证描述信息,输入认证凭证查询条件,E,凭证查询条件,凭证ID,1
,,,,,,,,读取认证凭证描述信息,R,认证凭证描述信息,凭证ID、当前描述信息,1
,,,,,,管理员在认证凭证编辑页面点击保存按钮，更新认证凭证描述信息,更新认证凭证描述信息,输入新认证凭证描述信息,E,新描述信息,凭证ID、新描述内容,1
,,,,,,,,验证描述信息格式有效性,X,验证结果,格式校验状态,1
,,,,,,,,写入更新后的认证凭证描述信息,W,认证凭证描述信息,凭证ID、更新后的描述信息,1
,,启用应用认证凭证,启用停用的认证凭证，通知认证中心，启用该凭证认证授权,3.0,发起者：管理员，接收者：密码应用管理系统-认证凭证管理模块,管理员在认证凭证管理界面点击启用按钮，选择停用的认证凭证进行启用操作,启用停用的认证凭证,查询停用的认证凭证列表,E,分页查询条件,页码、单页数量、状态（停用）,1
,,,,,,,,读取选中的停用凭证详细信息,R,认证凭证信息,凭证ID、凭证名称、当前状态、创建时间、关联应用,1
,,,,,,,,验证凭证启用条件,R,系统配置规则,启用限制规则、关联应用状态,1
,,,,,,,,更新凭证状态为启用,W,认证凭证信息,凭证ID、新状态（启用）、操作时间、操作人,1
,,,,,,,,记录凭证启用操作日志,W,系统操作日志,操作类型、操作时间、操作人、凭证ID、旧状态、新状态,1
,,,,,发起者：密码应用管理系统，接收者：认证中心,系统检测到认证凭证状态更新为启用,通知认证中心启用凭证,生成凭证启用通知请求,E,通知请求参数,凭证ID、启用状态、关联应用标识,1
,,,,,,,,调用认证中心接口发送启用通知,X,认证中心接口响应,请求状态码、响应消息,1
,,,,,,,,记录认证中心接口调用日志,W,接口调用日志,调用时间、接口名称、请求参数、响应结果,1
,,停用应用认证凭证,停用认证凭证，通知认证中心，禁止该凭证认证授权,,发起者：管理员，接收者：密码应用数据管理系统,管理员在认证凭证管理页面点击停用按钮,停用认证凭证并通知认证中心,选择待停用的认证凭证,E,认证凭证标识,凭证ID、凭证名称,1
,,,,,,,,读取认证凭证当前状态,R,认证凭证状态信息,凭证ID、当前状态（启用/停用）,1
,,,,,,,,更新认证凭证状态为停用,W,认证凭证状态变更记录,凭证ID、原状态、新状态（停用）,1
,,,,,,,,生成认证中心通知请求,X,认证中心通知消息,凭证ID、操作类型（停用）,1
,,删除应用认证凭证,删除认证凭证，同步清除认证中心中的对应认证凭证,4.0,发起者：管理员，接收者：密码应用数据管理系统-密码应用管理模块,管理员在应用认证凭证管理页面点击删除按钮，选择要删除的认证凭证,删除认证凭证并同步清除认证中心凭证,输入待删除的认证凭证标识,E,认证凭证信息,凭证ID、凭证名称,1
,,,,,,,,读取本地认证凭证数据,R,认证凭证信息,凭证ID、凭证名称、凭证内容、创建时间,1
,,,,,,,,删除本地认证凭证记录,W,认证凭证信息,凭证ID、凭证名称,1
,,,,,,,,向认证中心发送删除请求,X,认证中心凭证信息,凭证ID、凭证名称,1
,,,,,,,,接收认证中心删除响应,R,认证中心响应信息,操作状态、错误代码,1
,,应用认证凭证完整性校验,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3.0,发起者：系统管理员，接收者：密码应用数据管理系统,系统管理员发起认证凭证完整性校验请求,验证认证凭证数据完整性,读取认证凭证原始数据,R,认证凭证数据,凭证ID、凭证内容、原始哈希值,1
,,,,,,,,计算当前凭证哈希值,E,计算参数,加密算法类型、凭证内容,1
,,,,,,,,比对原始哈希与当前哈希,R,哈希校验结果,哈希匹配状态,1
,,,,,发起者：系统，接收者：密码应用数据管理系统,系统检测到认证凭证哈希值不匹配,处理凭证篡改异常,记录篡改异常日志,W,安全日志,异常时间、凭证ID、操作用户,1
,,,,,,,,生成异常提示信息,E,异常信息,异常类型、凭证ID,1
,,,,,,,,输出凭证异常警告,X,用户界面,异常提示文本、凭证ID,1
,,密码应用业务功能列表,处理应用业务和密码服务集群的绑定关系，安装服务类型进行绑定；列表展示内容：应用业务类型、密码服务集群名称,,发起者：管理员，接收者：密码应用管理系统,管理员点击密码应用业务功能列表菜单,查看密码应用业务功能列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用业务与密码服务集群绑定数据,R,绑定关系数据,应用业务类型、密码服务集群名称、安装服务类型,1
,,,,,,,,输出密码应用业务功能列表,X,列表展示数据,应用业务类型、密码服务集群名称,1
,,,,,,管理员在密码应用业务功能列表页面点击绑定按钮，配置密码服务集群,配置密码应用业务与密码服务集群绑定,输入应用业务类型,E,应用业务信息,应用业务类型,1
,,,,,,,,输入密码服务集群名称,E,密码服务集群信息,密码服务集群名称,1
,,,,,,,,输入安装服务类型,E,服务类型信息,安装服务类型,1
,,,,,,,,保存密码应用业务与密码服务集群绑定关系,W,绑定关系数据,应用业务类型、密码服务集群名称、安装服务类型,1
,,新增密码应用业务功能,下拉选择业务类型、下拉选择密码服务集群,4.0,,管理员在新增密码应用业务功能页面点击保存按钮,创建密码应用业务功能,输入业务类型和密码服务集群信息,E,业务配置信息,业务类型名称、业务类型ID、集群名称、集群IP地址、集群端口,1
,,,,,,,,读取可用业务类型列表,R,业务类型目录,业务类型名称、业务类型ID、描述,1
,,,,,,,,读取可用密码服务集群列表,R,密码服务集群信息,集群名称、集群IP地址、集群端口、集群状态,1
,,,,,,,,验证业务类型与集群的关联性,R,业务集群关联规则,业务类型ID、允许的集群ID列表,1
,,,,,,,,写入新的密码应用业务功能配置,W,密码应用业务功能表,业务功能ID、业务类型ID、集群ID、创建时间,1
,,删除密码应用业务功能,删除应用业务和密码服务集群的绑定关系,3.0,发起者：管理员，接收者：密码应用管理模块,管理员在密码应用管理界面点击删除按钮，选择要删除的应用业务和密码服务集群的绑定关系,删除应用业务与密码服务集群的绑定关系,输入删除绑定关系的条件,E,删除条件,应用业务ID、密码服务集群ID,1
,,,,,,,,读取现有的绑定关系信息,R,绑定关系信息,应用业务ID、密码服务集群ID、绑定状态,1
,,,,,,,,删除绑定关系记录,W,绑定关系记录,应用业务ID、密码服务集群ID,1
,,,,,,,,输出删除结果,X,操作结果,状态码、消息,1
,密码应用场景管理,密码应用场景分页列表查询,展示密码应用场景信息，展示内容：序号、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用场景分页列表查询菜单,查询密码应用场景分页列表,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用场景信息,R,密码应用场景信息,序号、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,1
,,,,,,,,输出密码应用场景分页列表,X,密码应用场景信息,序号、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,1
,,新建密码应用场景,新建密码应用场景，录入内容：业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,4.0,,管理员点击新建密码应用场景按钮并填写表单,创建密码应用场景,输入密码应用场景信息,E,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,,,,,,,保存密码应用场景信息,W,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,编辑密码应用场景,编辑密码应用场景，编辑内容：业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,3.0,,管理员在密码应用场景管理页面点击编辑按钮，修改密码应用场景信息,编辑密码应用场景信息,输入编辑的密码应用场景信息,E,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,,,,,,,读取原始密码应用场景信息,R,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,,,,,,,更新密码应用场景信息,W,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,,,,,,,输出编辑成功提示,X,操作反馈信息,操作状态、操作时间,1
,,删除密码应用场景,删除密码应用场景,,,管理员在密码应用场景管理页面点击删除按钮，发起删除密码应用场景请求,删除密码应用场景,输入删除请求信息,E,删除请求信息,密码应用场景ID、用户身份标识,1
,,,,,,,,读取用户权限信息,R,用户权限信息,用户身份标识、操作权限类型,1
,,,,,,,,读取密码应用场景信息,R,密码应用场景信息,密码应用场景ID、场景名称、关联密码算法、创建时间,1
,,,,,,,,执行密码应用场景删除操作,W,密码应用场景信息,密码应用场景ID、删除状态标识,1
,,,,,,,,输出删除操作结果,X,操作结果信息,操作状态码、操作描述,1
,密码应用改造厂商管理,密码应用改造厂商分页列表查询,展示参与密码应用改造的厂商信息,4.0,发起者：用户，接收者：密码应用数据管理-密码应用改造厂商管理模块,用户点击密码应用改造厂商分页列表查询菜单,查看密码应用改造厂商分页列表,输入分页查询参数,E,分页信息,页码、每页数量,1
,,,,,,,,读取密码应用改造厂商信息,R,密码应用改造厂商信息,厂商名称、联系方式、改造状态、所属行业、认证状态,1
,,新增密码应用改造厂商,新增密码应用改造的厂商信息,3.0,发起者：管理员，接收者：密码应用数据管理系统-密码应用改造厂商管理模块,管理员在密码应用改造厂商管理页面点击新增厂商按钮,新增密码应用改造厂商信息,输入厂商基本信息,E,厂商信息,厂商名称、统一社会信用代码、法定代表人、注册地址、联系方式,1
,,,,,,,,验证厂商信息完整性,R,厂商信息,厂商名称、统一社会信用代码,1
,,,,,,,,保存厂商信息,W,厂商信息,厂商名称、统一社会信用代码、法定代表人、注册地址、联系方式,1
,,编辑密码应用改造厂商,编辑密码应用改造的厂商信息,,发起者：管理员，接收者：密码应用数据管理模块,管理员在密码应用改造厂商管理页面点击编辑按钮，选择需要编辑的厂商,编辑密码应用改造厂商信息,输入厂商ID或名称以选择目标厂商,E,厂商标识信息,厂商ID、厂商名称,1
,,,,,,,,读取目标厂商的当前信息,R,厂商详细信息,厂商名称、联系方式、服务内容、认证状态、合作时间,1
,,,,,,,,输出厂商当前信息至编辑界面,X,厂商详细信息,厂商名称、联系方式、服务内容、认证状态、合作时间,1
,,,,,,,,输入更新后的厂商信息,E,厂商详细信息,厂商名称、联系方式、服务内容、认证状态、合作时间,1
,,,,,,,,写入更新后的厂商信息至数据库,W,厂商详细信息,厂商名称、联系方式、服务内容、认证状态、合作时间,1
,,删除密码应用改造厂商,删除密码应用改造的厂商信息,,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用改造厂商管理页面点击删除按钮,删除密码应用改造厂商信息,选择待删除的密码应用改造厂商,E,厂商信息,厂商ID、厂商名称,1
,,,,,,,,读取待删除的密码应用改造厂商信息,R,厂商详细信息,厂商ID、厂商名称、改造项目列表、关联数据状态,1
,,,,,,,,执行密码应用改造厂商删除操作,W,厂商信息,厂商ID、删除状态标记,1
,,,,,,,,输出厂商删除结果,X,操作结果,操作状态、错误信息（如有）,1
密码资产数据管理,密码资产名称管理,密码服务列表,平台中密码服务资源的列表信息，列表内容：序号、服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击密码服务列表菜单,查看密码服务列表信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码服务列表数据,R,密码服务列表信息,序号、服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
,,,,,,管理员在密码服务列表页面点击新增/编辑按钮，配置密码服务信息,配置密码服务信息,输入密码服务基本信息,E,密码服务信息,服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注,1
,,,,,,,,保存密码服务配置,W,密码服务信息,服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注,1
,,密码服务查询,支持根据服务名称、服务类型、ip地址、端口进行检测,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务查询页面输入服务名称、服务类型、IP地址、端口等条件并提交查询,查询密码服务信息,输入查询条件,E,查询条件,服务名称、服务类型、IP地址、端口,1
,,,,,,,,读取密码服务信息,R,密码服务信息,服务名称、服务类型、IP地址、端口、服务状态、创建时间,1
,,,,,,,,输出查询结果,X,查询结果,匹配的密码服务列表、匹配数量,1
,,,,,,管理员在密码服务查询页面点击检测按钮，验证服务配置有效性,检测密码服务配置,输入检测参数,E,检测参数,服务名称、IP地址、端口,1
,,,,,,,,执行连通性检测,X,检测结果,检测状态、错误信息,1
,,密码服务状态检测,定期通过rest接口检测密码服务状态,4.0,发起者：系统管理员，接收者：密码资产管理系统,系统定时任务触发密码服务状态检测,执行密码服务状态检测,读取配置的检测任务信息,R,检测任务配置,任务名称、检测频率、目标服务URL、请求方法、认证信息,1
,,,,,,,,构建REST请求参数,E,REST请求参数,请求URL、HTTP方法、请求头、请求体,1
,,,,,,,,发送REST请求,X,网络通信,请求数据包,1
,,,,,,,,接收REST响应,X,网络通信,响应状态码、响应头、响应体,1
,,,,,,,,解析响应数据,R,响应解析结果,服务状态、错误代码、响应时间,1
,,,,,,,,记录检测结果,W,检测结果记录,服务名称、检测时间、状态、响应时间、错误详情,1
,,,,,,用户点击手动检测按钮,生成检测报告,读取检测结果,R,检测结果记录,服务名称、检测时间、状态、响应时间、错误详情,1
,,,,,,,,生成报告内容,E,检测报告,报告标题、检测时间范围、服务状态统计、异常列表,1
,,,,,,,,保存检测报告,W,检测报告存储,报告名称、生成时间、报告内容,1
,,新建密码服务,新增密码服务资源，新增录入内容：服务名称、区域、服务类型、服务集群、设备集群、根据服务类型中部署类型决定展示服务规格还是ip端口、备注、创建数量,10.0,发起者：管理员，接收者：密码资产数据管理系统,管理员点击新建密码服务按钮,新增密码服务资源,输入密码服务基本信息,E,密码服务基本信息,服务名称、区域、服务类型、服务集群、设备集群、备注、创建数量,1
,,,,,,,,读取服务类型中的部署类型,R,服务类型配置,部署类型,1
,,,,,,,,根据部署类型输入服务规格或IP端口,E,服务规格/IP端口信息,服务规格（如CPU/内存）或IP地址、端口号,1
,,,,,,,,保存密码服务资源信息,W,密码服务资源信息,服务名称、区域、服务类型、服务集群、设备集群、服务规格/IP端口、备注、创建数量,1
,,编辑密码服务,编辑密码服务资源，编辑内容：服务名称、备注,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在密码服务管理页面点击保存按钮，提交编辑后的密码服务信息,编辑密码服务资源,输入编辑后的密码服务信息,E,密码服务信息,服务名称、备注,1
,,,,,,,,验证输入的密码服务信息有效性,R,密码服务信息,服务名称、备注,1
,,,,,,,,更新密码服务信息到数据库,W,密码服务信息,服务名称、备注,1
,,重启密码服务,重启密码服务容器,,发起者：管理员，接收者：密码资产数据管理系统-密码服务管理模块,管理员在密码服务管理页面点击重启按钮,确认重启密码服务,输入重启确认信息,E,密码服务操作请求,服务名称、确认状态,1
,,,,,,,,读取重启确认请求,R,密码服务操作请求,服务名称、确认状态,1
,,,,,,,,显示重启确认提示,X,操作确认响应,确认提示信息,1
,,,,,,管理员在确认提示后点击确定按钮执行重启,执行密码服务重启,发送重启指令,E,密码服务控制指令,服务标识符、操作类型,1
,,,,,,,,执行容器重启操作,W,容器管理接口,容器ID、重启参数,1
,,,,,,,,返回重启结果,X,操作结果响应,重启状态、错误信息,1
,,启动密码服务,启动停止的密码服务容器,3.0,发起者：系统管理员，接收者：密码资产管理系统-密码资产名称管理模块,系统管理员在密码服务管理页面点击启动按钮，启动停止的密码服务容器,启动密码服务容器,输入启动请求,E,启动请求,服务名称,1
,,,,,,,,读取服务状态,R,服务状态信息,服务名称、当前状态,1
,,,,,,,,执行服务启动,W,服务状态信息,服务名称、目标状态（运行中）,1
,,,,,,,,输出启动结果,X,启动结果,服务名称、最终状态,1
,,停止密码服务,停止密码服务容器,4.0,发起者：管理员，接收者：密码资产管理模块,管理员在密码服务管理页面点击停止服务按钮，以停止指定的密码服务容器,停止密码服务容器,选择密码服务容器,E,密码服务信息,服务名称、服务ID,1
,,,,,,,,验证服务状态,R,服务状态信息,服务状态、运行状态,1
,,,,,,,,执行停止操作,W,服务控制指令,操作类型（停止）、目标服务ID,1
,,,,,,,,确认停止成功,R,服务状态信息,服务状态、停止时间,1
,,更新密码服务规格,修改密码服务运行容器的资源规格，实现单个密码服务的动态扩容、缩容,8.0,发起者：管理员，接收者：密码资产管理系统,管理员在密码服务管理页面点击更新规格按钮,查看密码服务当前资源规格,查询密码服务资源规格,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码服务资源规格,R,密码服务资源规格,服务名称、CPU核数、内存大小、实例数量,1
,,,,,,管理员在密码服务管理页面输入新的资源规格参数,更新密码服务资源规格,输入新资源规格参数,E,密码服务资源规格,服务名称、CPU核数、内存大小、实例数量,1
,,,,,,,,验证资源规格参数有效性,X,校验结果,参数合法性状态、错误信息,1
,,,,,,,,更新密码服务资源规格,W,密码服务资源规格,服务名称、CPU核数、内存大小、实例数量,1
,,,,,,,,输出更新结果,X,操作结果,更新状态、操作时间,1
,,删除密码服务,删除停止的密码服务,4.0,发起者：管理员，接收者：密码资产数据管理系统-密码资产名称管理模块,管理员在密码服务管理页面点击删除按钮,删除停止的密码服务,输入密码服务信息,E,密码服务信息,服务名称、服务ID,1
,,,,,,,,读取密码服务状态,R,密码服务状态,服务状态,1
,,,,,,,,验证服务是否处于停止状态,X,状态验证结果,验证结果（是否停止）,1
,,,,,,,,删除密码服务记录,W,密码服务信息,服务名称、服务ID,1
,,,,,,,,输出删除操作结果,X,操作结果,删除状态（成功/失败）、错误信息,1
,,密码服务服务组新增,新增服务组，配置数据库,,发起者：系统管理员，接收者：密码资产数据管理系统,系统管理员在密码服务服务组管理界面点击『新增服务组』按钮,新增密码服务服务组并配置数据库,输入服务组基本信息,E,服务组信息,服务组名称、服务组描述,1
,,,,,,,,读取现有服务组信息,R,服务组信息,服务组名称、服务组描述,1
,,,,,,,,输入数据库连接信息,E,数据库配置信息,数据库地址、数据库端口、数据库用户名、数据库密码,1
,,,,,,,,验证数据库连接,X,数据库验证结果,验证状态、错误信息,1
,,,,,,,,保存服务组与数据库配置,W,服务组信息、数据库配置信息,服务组名称、服务组描述、数据库地址、数据库端口、数据库用户名,1
,,密码服务服务组列表,列表展示服务组信息，包括服务组标识、服务组名称、业务类型、服务数量等信息,,发起者：用户，接收者：密码资产数据管理模块,用户点击密码服务服务组列表菜单,展示密码服务服务组列表,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页信息,R,分页信息,页码、单页数量,1
,,,,,,,,查询密码服务服务组信息,R,服务组信息,服务组标识、服务组名称、业务类型、服务数量,1
,,,,,,,,输出服务组列表,X,服务组列表,服务组标识、服务组名称、业务类型、服务数量,1
,,密码服务服务组编辑,编辑服务组名称,2.0,发起者：管理员，接收者：密码资产数据管理系统-密码服务服务组编辑模块,管理员在服务组管理页面点击编辑服务组名称按钮,编辑服务组名称,输入新的服务组名称,E,服务组信息,服务组名称,1
,,,,,,,,验证服务组名称是否已存在,R,服务组信息,服务组名称,1
,,,,,,,,保存更新后的服务组名称,W,服务组信息,服务组名称,1
,,密码服务管理列表,查看服务组内的密码服务,,发起者：用户，接收者：密码资产数据管理模块-密码服务管理子模块,用户点击密码服务管理列表菜单,查看服务组内的密码服务,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取服务组密码服务数据,R,密码服务信息,服务名称、服务类型、服务状态、创建时间,1
,,,,,,,,输出密码服务列表,X,密码服务列表,服务名称、服务类型、服务状态、创建时间,1
,,密码服务释放,从服务组释放密码服务，,3.0,发起者：管理员，接收者：密码资产管理系统,管理员在服务组管理页面点击密码服务释放按钮,查看服务组中的密码服务,查询服务组密码服务列表,E,分页信息,页码、单页数量,1
,,,,,,,,读取服务组密码服务信息,R,服务组密码服务信息,服务组ID、服务组名称、服务ID、服务名称、服务状态,1
,,,,,,管理员在服务组密码服务列表中选择服务并点击释放按钮,释放密码服务,输入待释放服务ID,E,服务信息,服务ID,1
,,,,,,,,验证服务是否存在,R,服务组密码服务信息,服务ID、服务状态,1
,,,,,,,,更新服务状态为已释放,W,服务组密码服务信息,服务ID、服务状态,1
,,,,,,,,输出服务释放结果,X,操作结果,操作状态、错误信息,1
,,密码服务镜像列表,展示密码服务的镜像列表，包括文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注等信息,4.0,发起者：用户，接收者：密码资产数据管理-密码服务镜像管理模块,用户点击密码服务镜像列表菜单,查看密码服务镜像列表,查询密码服务镜像列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码服务镜像列表数据,R,密码服务镜像信息,文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注,1
,,,,,,用户在密码服务镜像列表页面点击新增/编辑镜像按钮,配置密码服务镜像信息,输入密码服务镜像信息,E,密码服务镜像信息,文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注,1
,,,,,,,,保存密码服务镜像信息,W,密码服务镜像信息,文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注,1
,,密码服务镜像上传,上传密码服务镜像并校验文件摘要,5.0,发起者：用户，接收者：密码资产数据管理系统,用户点击密码服务镜像上传按钮并选择文件,上传密码服务镜像文件,输入密码服务镜像文件,E,镜像文件,文件名、文件大小、文件内容,1
,,,,,,,,接收并存储镜像文件,W,镜像文件存储,存储路径、文件哈希值,1
,,,,,,,,读取镜像文件元数据,R,镜像文件元数据,文件类型、上传时间,1
,,,,,发起者：密码资产数据管理系统，接收者：密码资产数据管理系统,系统检测到镜像文件上传完成,校验文件摘要,读取镜像文件内容,R,镜像文件,文件内容,1
,,,,,,,,计算文件摘要值,X,摘要计算,摘要算法类型、摘要值,1
,,,,,,,,读取预期摘要值,R,摘要配置,预期摘要值,1
,,,,,,,,比较实际摘要与预期摘要,X,摘要校验,校验结果,1
,,密码服务镜像编辑,编辑备注信息,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在密码服务镜像编辑页面点击保存按钮，编辑密码服务镜像的备注信息,编辑密码服务镜像备注信息,读取当前密码服务镜像的备注信息,R,密码服务镜像备注信息,密码服务镜像ID、备注内容,1
,,,,,,,,输入修改后的备注信息,E,密码服务镜像备注信息,备注内容,1
,,,,,,,,更新密码服务镜像的备注信息,W,密码服务镜像备注信息,密码服务镜像ID、备注内容,1
,,密码服务镜像查询,根据镜像名称、服务类型进行查询,,,管理员在密码服务镜像查询页面输入镜像名称和服务类型并提交查询,查询密码服务镜像信息,输入查询条件,E,查询条件,镜像名称、服务类型,1
,,,,,,,,读取密码服务镜像数据,R,密码服务镜像数据,镜像名称、服务类型、镜像版本、创建时间、状态,1
,,,,,,,,输出查询结果,X,查询结果,镜像名称、服务类型、镜像版本、创建时间、状态,1
,,密码服务镜像启用,启用密码服务镜像,,发起者：管理员，接收者：密码资产管理系统-密码资产数据管理模块,管理员在密码服务镜像管理页面点击启用镜像按钮,启用密码服务镜像,输入密码服务镜像信息,E,密码服务镜像信息,镜像名称、版本、配置参数,1
,,,,,,,,验证镜像配置有效性,R,镜像配置规则,版本兼容性规则、参数校验规则,1
,,,,,,,,保存启用的镜像配置,W,密码服务镜像配置,镜像名称、版本、配置参数,1
,,,,,,,,输出镜像启用结果,X,操作结果,启用成功/失败状态、错误信息,1
,,密码服务镜像禁用,禁用密码服务镜像,,发起者：管理员，接收者：密码资产数据管理系统-密码资产名称管理模块,管理员在密码服务镜像管理页面点击禁用按钮,禁用密码服务镜像,选择密码服务镜像,E,密码服务镜像信息,镜像名称、镜像ID,1
,,,,,,,,读取密码服务镜像状态,R,密码服务镜像状态,当前状态（启用/禁用）,1
,,,,,,,,更新密码服务镜像状态为禁用,W,密码服务镜像状态,镜像名称、镜像ID、状态（禁用）,1
,,,,,,,,返回禁用操作结果,X,操作结果,操作状态（成功/失败）、错误信息,1
,,密码服务镜像删除,删除密码服务镜像,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员在密码服务镜像管理页面点击删除按钮,删除密码服务镜像,输入密码服务镜像标识信息,E,镜像信息,镜像ID、镜像名称、镜像版本,1
,,,,,,,,读取密码服务镜像元数据,R,镜像元数据,镜像存储路径、镜像依赖项、镜像状态,1
,,,,,,,,输出删除确认信息,X,操作确认信息,确认提示内容、镜像名称、删除操作类型,1
,,,,,,,,执行密码服务镜像删除操作,W,镜像存储记录,镜像ID、存储路径、删除时间戳,1
,密码资产数据管理,密码服务数据库新增,新增数据库信息，选择数据库类型，输入数据库IP、端口，管理员账号、密码,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码服务数据库管理页面点击新增数据库按钮,新增密码服务数据库信息,选择数据库类型,E,数据库类型信息,数据库类型名称（如MySQL/Oracle/PostgreSQL等）,1
,,,,,,,,输入数据库IP地址,E,数据库信息,数据库IP地址,1
,,,,,,,,输入数据库端口号,E,数据库信息,数据库端口号,1
,,,,,,,,输入管理员账号,E,数据库信息,管理员账号名称,1
,,,,,,,,输入管理员密码,E,数据库信息,管理员密码,1
,,,,,,,,保存数据库配置信息,W,数据库信息,数据库类型、IP地址、端口号、管理员账号、管理员密码,1
,,密码服务数据库列表,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,,,管理员点击密码服务数据库列表菜单,查看密码服务数据库列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码服务数据库信息,R,密码服务数据库信息,数据库名称、数据库类型、实例库名称、数据库IP端口、完整性校验状态,1
,,,,,,,,输出密码服务数据库列表,X,密码服务数据库列表,数据库名称、数据库类型、实例库名称、数据库IP端口、完整性校验状态,1
,,,,,发起者：系统，接收者：密码资产数据管理系统,系统定时触发数据库完整性校验,校验密码服务数据库完整性,读取待校验的数据库信息,R,密码服务数据库信息,数据库名称、数据库IP端口、校验规则,1
,,,,,,,,执行完整性校验逻辑,X,校验结果,数据库名称、校验状态、异常描述,1
,,,,,,,,更新数据库完整性状态,W,密码服务数据库信息,数据库名称、完整性校验状态,1
,,密码服务数据库模式列表,列表展示密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产数据管理系统-密码服务数据库模式模块,管理员点击密码服务数据库模式列表菜单,查看密码服务数据库模式列表,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码服务数据库模式信息,R,密码服务数据库模式信息,模式名称、模式描述、创建时间、更新时间,1
,,,,,,,,输出密码服务数据库模式列表,X,密码服务数据库模式列表数据,模式名称、模式描述、创建时间、更新时间,1
,,密码服务数据库模式删除,删除密码服务数据库模式,,发起者：管理员，接收者：密码资产数据管理系统,管理员在数据库管理页面点击删除模式按钮,删除密码服务数据库模式,输入数据库模式删除请求,E,数据库模式信息,模式名称、确认标志,1
,,,,,,,,读取数据库模式元数据,R,数据库模式元数据,模式名称、关联表数量、依赖关系,1
,,,,,,,,执行数据库模式删除操作,W,数据库模式信息,模式名称、删除状态,1
,,,,,,,,输出删除操作结果,X,操作结果信息,状态码、操作描述、错误信息,1
,,密码服务数据库模式查询,查询密码服务数据库模式,,发起者：数据库管理员，接收者：密码资产数据管理系统,数据库管理员点击数据库模式查询菜单,查询密码服务数据库模式,输入数据库模式查询条件,E,查询条件,数据库名称、模式名称,1
,,,,,,,,读取密码服务数据库模式信息,R,数据库模式信息,表名、字段名、数据类型、主键、外键,1
,,,,,,,,输出数据库模式查询结果,X,查询结果,结构化数据库模式数据,1
,,密码服务数据库模式新增,新增数据库模式,,发起者：数据库管理员，接收者：密码服务数据库管理系统,数据库管理员在数据库管理界面点击新增数据库模式按钮,新增数据库模式,输入数据库模式信息,E,数据库模式信息,模式名称、描述、版本号、创建时间,1
,,,,,,,,读取现有数据库模式信息,R,现有数据库模式信息,模式名称、版本号,1
,,,,,,,,写入新的数据库模式信息,W,数据库模式信息,模式名称、描述、版本号、创建时间,1
,,,,,,,,输出操作结果,X,操作结果信息,操作状态、错误代码、提示信息,1
,,API网关列表,列表内容：名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP（列表不显示）、端口（列表不显示）、反向代理地址端口（列表不显示）,4.0,发起者：用户，接收者：密码资产数据管理系统-API网关管理模块,用户点击API网关列表菜单,查看API网关列表信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取API网关基础信息,R,API网关信息,名称、所属区域、标识、类型、IP、业务端口、管理端口,1
,,,,,,,,输出API网关列表数据,X,API网关列表,名称、所属区域、标识、类型、IP、业务端口、管理端口,1
,,,,,,用户在API网关列表页面点击新增/编辑按钮配置网关信息,配置API网关信息,输入API网关配置信息,E,API网关配置,名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP、端口、反向代理地址端口,1
,,,,,,,,写入API网关配置信息,W,API网关配置,名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP、端口、反向代理地址端口,1
,,API网关初始化,密码服务平台部署成功后，如选择部署API网关，根据平台部署信息，自动加载对应的部署网关信息,3.0,发起者：系统管理员，接收者：密码服务平台-密码资产数据管理模块,系统检测到密码服务平台部署完成且用户选择部署API网关,初始化API网关配置,读取平台部署信息,R,平台部署配置,网关部署标识、网关类型、网关IP地址、网关端口号、部署路径,1
,,,,,,,,生成API网关初始化参数,E,网关初始化参数,网关类型、IP地址、端口号、部署路径、安全协议版本,1
,,,,,,,,执行API网关初始化,X,网关初始化结果,初始化状态、错误代码、日志信息,1
,,,,,,用户手动触发API网关配置更新,更新API网关配置,输入新的网关配置参数,E,网关配置参数,新IP地址、新端口号、部署路径、安全协议版本,1
,,,,,,,,验证配置参数有效性,R,配置校验规则,IP地址格式、端口范围、路径合法性,1
,,,,,,,,更新网关配置存储,W,网关配置存储,更新后的IP地址、端口号、部署路径,1
,,API网关新增,新增API网关信息，录入名称、所属需求、标识、类型（管理、业务）、管理端口,,发起者：管理员，接收者：密码资产数据管理系统-API网关管理模块,管理员在API网关管理页面点击新增按钮，录入API网关信息,录入并保存API网关信息,输入API网关信息,E,API网关信息,名称、所属需求、标识、类型、管理端口,1
,,,,,,,,保存API网关信息,W,API网关信息,名称、所属需求、标识、类型、管理端口,1
,,API网关编辑,编辑内容：网关名称、管理端口,nan,发起者：管理员，接收者：密码资产数据管理系统-API网关模块,管理员在API网关管理页面点击编辑按钮，进入编辑页面,查看API网关当前配置信息,查询API网关信息,E,查询条件,网关ID,1
,,,,,,,,读取API网关信息,R,网关信息,网关名称、管理端口,1
,,,,,,管理员在API网关编辑页面点击保存按钮，提交修改后的网关信息,编辑并保存API网关信息,输入网关信息,E,网关信息,网关名称、管理端口,1
,,,,,,,,验证网关信息,X,验证结果,格式校验状态、端口可用性状态,1
,,,,,,,,保存网关信息,W,网关信息,网关名称、管理端口,1
,,API网关删除,删除网关信息,3.0,发起者：管理员，接收者：密码资产管理系统-API网关管理模块,管理员在API网关管理页面点击删除按钮,确认删除API网关信息,输入待删除的API网关标识,E,API网关标识,网关ID、网关名称,1
,,,,,,,,读取待删除的API网关信息,R,API网关信息,网关ID、网关名称、配置详情,1
,,,,,,管理员在确认删除对话框点击确定按钮,执行删除API网关信息,删除API网关配置数据,W,API网关信息,网关ID、网关名称,1
,,,,,,,,输出删除操作结果,X,操作结果,状态、消息,1
,,路由管理列表,展示内容：路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击路由管理列表菜单,查看路由配置信息,查询路由分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取路由配置数据,R,路由信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1
,,,,,,管理员在路由管理列表页面点击新增/编辑路由按钮,配置路由信息,输入路由基本信息,E,路由基础信息,路由名称、路由组件标识、服务类型,1
,,,,,,,,关联应用与服务组,E,关联配置,所属应用、所属服务组,1
,,,,,,,,配置路由路径与规则,E,路由规则,URL路径、匹配条件、超时时间,1
,,,,,,,,保存路由配置,W,路由信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、匹配条件、超时时间,1
,,路由管理详情,展示路由管理详情，包含服务列表信息、应用信息,,发起者：管理员，接收者：密码资产数据管理系统,管理员点击路由管理详情菜单,查看路由管理详情信息,查询服务列表信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取服务列表信息,R,服务列表信息,服务名称、服务IP、服务端口、服务状态,1
,,,,,,,,查询应用信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取应用信息,R,应用信息,应用名称、所属服务、应用类型、部署环境,1
,,设备类型展示,内容：设备类型名称、所属厂商（录入）、设备类型（云密码机、物理密码机、虚拟密码机）、管理接口协同（HTTPS、HTTP）、管理端口；云密码机、虚拟密码机、物理密码机类型配置信息不同,,,管理员点击设备类型展示菜单,查看设备类型信息,输入查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取设备类型数据,R,设备类型信息,设备类型名称、所属厂商、设备类型（云密码机/物理密码机/虚拟密码机）、管理接口协同（HTTPS/HTTP）、管理端口,1
,,,,,,管理员在设备类型展示页面点击新增/编辑设备类型按钮,配置设备类型信息,输入设备类型信息,E,设备类型信息,设备类型名称、所属厂商、设备类型（云密码机/物理密码机/虚拟密码机）、管理接口协同（HTTPS/HTTP）、管理端口,1
,,,,,,,,保存设备类型配置,W,设备类型信息,设备类型名称、所属厂商、设备类型（云密码机/物理密码机/虚拟密码机）、管理接口协同（HTTPS/HTTP）、管理端口,1
,,设备类型初始化,根据平台支持的设备类型，平台部署时，初始化平台默认支持的设备类型,,发起者：系统管理员，接收者：密码资产数据管理系统,系统管理员在平台部署时执行设备类型初始化命令,初始化平台默认支持的设备类型,读取设备类型配置文件,E,设备类型配置文件,设备类型名称、设备型号、支持的密码算法、通信协议版本,1
,,,,,,,,解析设备类型配置数据,R,设备类型配置数据,设备类型名称、设备型号、支持的密码算法、通信协议版本,1
,,,,,,,,验证设备类型数据有效性,E,校验规则,必填字段校验规则、格式校验规则、密码算法兼容性规则,1
,,,,,,,,写入设备类型信息到数据库,W,设备类型信息,设备类型名称、设备型号、支持的密码算法、通信协议版本,1
,,设备类型新增,添加设备对应信息,,发起者：管理员，接收者：密码资产数据管理系统,管理员在设备类型管理页面点击新增设备类型按钮,新增设备类型信息,输入设备类型基础信息,E,设备类型信息,设备类型名称、型号、厂商、描述,1
,,,,,,,,校验设备类型唯一性,R,设备类型信息,设备类型名称、型号,1
,,,,,,,,保存设备类型信息,W,设备类型信息,设备类型名称、型号、厂商、描述,1
,,,,,,,,返回新增结果,X,操作结果,操作状态、提示信息,1
,,设备类型编辑,编辑设备相关信息,,发起者：管理员，接收者：密码资产数据管理系统-设备类型编辑模块,管理员点击设备类型编辑菜单,查看设备类型信息,输入设备类型查询条件,E,查询参数,设备类型ID、设备类型名称,1
,,,,,,,,读取设备类型信息,R,设备类型信息,设备类型名称、设备类型描述、设备参数模板,1
,,,,,,,,输出设备类型信息,X,设备类型信息,设备类型名称、设备类型描述、设备参数模板,1
,,,,,,管理员在设备类型编辑页面点击保存按钮,编辑设备类型信息,输入设备类型信息,E,设备类型信息,设备类型名称、设备类型描述、设备参数模板,1
,,,,,,,,验证设备类型信息,R,校验规则,名称唯一性校验规则、参数格式校验规则,1
,,,,,,,,保存设备类型信息,W,设备类型信息,设备类型名称、设备类型描述、设备参数模板,1
,,,,,,,,输出保存结果,X,操作结果,操作状态、错误信息,1
,,设备类型停用,停用设备类型不可再创建该类型的设备,2.0,发起者：管理员，接收者：密码资产数据管理系统-设备类型管理模块,管理员在设备类型管理界面点击停用按钮,停用设备类型,选择设备类型,E,设备类型信息,设备类型ID、设备类型名称,1
,,,,,,,,验证设备类型可停用性,R,依赖项信息,关联设备数量、关联服务数量,1
,,,,,,,,更新设备类型状态,W,设备类型状态,设备类型ID、状态（停用）,1
,,,,,发起者：用户，接收者：密码资产数据管理系统-设备创建模块,用户尝试创建设备时选择已停用的设备类型,阻止创建停用类型设备,读取设备类型有效性,R,设备类型状态,设备类型ID、状态,1
,,,,,,,,输出创建限制提示,X,错误信息,错误代码、错误描述（该设备类型已停用）,1
,,设备类型启用,启用停用的设备类型,3.0,发起者：管理员，接收者：密码资产数据管理系统-设备类型管理模块,管理员在设备类型管理页面点击启用/停用按钮,启用设备类型,选择设备类型,E,设备类型信息,设备类型ID、设备类型名称,1
,,,,,,,,读取设备类型状态,R,设备类型状态信息,设备类型ID、当前状态,1
,,,,,,,,更新设备类型状态为启用,W,设备类型状态信息,设备类型ID、目标状态,1
,,,,,,,,输出操作结果,X,操作反馈信息,操作状态、提示信息,1
,,,,,,,停用设备类型,选择设备类型,E,设备类型信息,设备类型ID、设备类型名称,1
,,,,,,,,读取设备类型状态,R,设备类型状态信息,设备类型ID、当前状态,1
,,,,,,,,更新设备类型状态为停用,W,设备类型状态信息,设备类型ID、目标状态,1
,,,,,,,,输出操作结果,X,操作反馈信息,操作状态、提示信息,1
,,设备类型删除,当无该类型的设备时，删除对应设备类型,,发起者：管理员，接收者：密码资产数据管理系统,管理员在设备类型管理界面点击删除按钮,删除设备类型,输入设备类型信息,E,设备类型信息,设备类型名称,1
,,,,,,,,查询设备数据,R,设备数据,设备类型名称、设备数量,1
,,,,,,,,删除设备类型,W,设备类型信息,设备类型名称,1
,,,,,,,,输出删除结果,X,操作结果,状态、消息,1
,,监控信息配置查看,查询当前监控信息的配置信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,4.0,,管理员点击监控信息配置查看菜单,查询监控方式配置信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取不同监控方式的配置信息,R,监控方式配置信息,监控方式名称、IP地址、端口号、协议类型、认证信息、采集频率,1
,,,,,,,,输出监控方式配置列表,X,监控方式配置列表,监控方式名称、IP地址、端口号、协议类型、状态标识,1
,,,,,,管理员在监控配置页面点击详情按钮查看具体监控方式配置,查看监控方式详细配置,输入监控方式标识,E,监控方式标识,监控方式ID,1
,,,,,,,,读取监控方式详细配置,R,监控方式详细配置,监控方式名称、IP地址、端口号、协议类型、认证信息、采集频率、告警阈值、关联资产,1
,,,,,,,,输出监控方式详细配置信息,X,监控方式详细配置,监控方式名称、IP地址、端口号、协议类型、认证信息、采集频率、告警阈值、关联资产,1
,,监控信息配置,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3.0,发起者：管理员，接收者：密码资产数据管理系统-监控信息配置模块,管理员点击监控信息配置菜单,查看监控信息配置,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取监控信息配置数据,R,监控信息配置,监控方式、设备类型、参数名称、参数值,1
,,,,,,管理员在监控信息配置页面点击新增按钮，配置设备监控信息,添加监控信息配置,选择监控方式,E,监控方式,SNMP、Rest接口、监控组件、监控探针,1
,,,,,,,,输入设备类型,E,设备类型,设备类型名称,1
,,,,,,,,输入监控参数,E,监控参数,参数名称、参数值,1
,,,,,,,,保存监控信息配置,W,监控信息配置,监控方式、设备类型、参数名称、参数值,1
,,,,,,管理员在监控信息配置页面点击编辑按钮，修改设备监控信息,编辑监控信息配置,选择待修改的监控信息配置,E,监控信息配置,配置ID,1
,,,,,,,,修改监控方式,E,监控方式,SNMP、Rest接口、监控组件、监控探针,1
,,,,,,,,修改设备类型,E,设备类型,设备类型名称,1
,,,,,,,,修改监控参数,E,监控参数,参数名称、参数值,1
,,,,,,,,更新监控信息配置,W,监控信息配置,监控方式、设备类型、参数名称、参数值,1
,,,,,,管理员在监控信息配置页面点击删除按钮，删除设备监控信息,删除监控信息配置,选择待删除的监控信息配置,E,监控信息配置,配置ID,1
,,,,,,,,删除监控信息配置,W,监控信息配置,配置ID,1
,,密码设备集群列表,列表内容：名称、设备类型、所属区域、设备数量、描述,4.0,发起者：管理员，接收者：密码资产数据管理系统-密码设备集群管理模块,管理员点击密码设备集群列表菜单,查看密码设备集群列表信息,查询密码设备集群分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码设备集群列表数据,R,密码设备集群信息,名称、设备类型、所属区域、设备数量、描述,1
,,,,,,管理员在密码设备集群管理页面点击添加集群按钮，配置密码设备集群信息,配置密码设备集群信息,输入密码设备集群信息,E,密码设备集群信息,名称、设备类型、所属区域、设备数量、描述,1
,,,,,,,,写入密码设备集群信息,W,密码设备集群信息,名称、设备类型、所属区域、设备数量、描述,1
,,密码设备集群新增,创建密码设备集群，录入名称、选择设备类型、所属区域、描述,3.0,发起者：管理员，接收者：密码资产数据管理系统,用户在密码设备集群管理页面点击新增按钮，创建密码设备集群,创建密码设备集群,输入密码设备集群基本信息,E,密码设备集群信息,集群名称、设备类型、所属区域、描述,1
,,,,,,,,保存密码设备集群信息,W,密码设备集群信息,集群名称、设备类型、所属区域、描述,1
,,,,,,,,输出创建结果,X,操作结果,创建成功/失败提示,1
,,密码设备集群编辑,可编辑内容：名称、描述,,发起者：用户，接收者：密码资产数据管理系统-密码设备集群管理模块,用户点击密码设备集群编辑按钮,编辑密码设备集群信息,输入密码设备集群名称,E,密码设备集群信息,名称,1
,,,,,,,,输入密码设备集群描述,E,密码设备集群信息,描述,1
,,,,,,用户点击密码设备集群保存按钮,保存密码设备集群信息,写入密码设备集群名称和描述,W,密码设备集群信息,名称、描述,1
,,密码设备集群删除,删除密码设备集群，需保障密码设备集群未被密码服务调用,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码设备集群管理页面点击删除按钮，选择目标集群,检查密码设备集群依赖关系,读取密码服务配置信息,R,密码服务配置,服务名称、关联集群ID,1
,,,,,,,,校验集群是否被服务引用,R,集群引用关系,集群ID、引用服务数量,1
,,,,,,管理员确认删除未被引用的密码设备集群,删除密码设备集群,输入待删除集群ID,E,集群标识信息,集群ID、删除确认标识,1
,,,,,,,,删除集群元数据,W,密码设备集群信息,集群ID、集群名称、设备列表,1
,,,,,,,,清理关联配置文件,W,集群配置文件,配置文件路径、集群ID,1
,,绑定密码设备,根据密码设备类型绑定密码设备，绑定密码设备后，根据类型配置判断是否需要进行保护密钥的创建和同步,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在密码设备管理界面点击绑定按钮并选择设备类型,绑定密码设备,选择密码设备类型,E,设备类型信息,设备类型名称、设备型号,1
,,,,,,,,输入密码设备基础信息,E,设备基础信息,设备名称、设备IP地址、设备端口号,1
,,,,,,,,验证设备类型配置,R,设备类型配置,是否需要创建保护密钥、是否需要同步密钥,1
,,,,,,,,保存密码设备绑定信息,W,绑定设备信息,设备类型、设备名称、设备IP地址、设备端口号,1
,,,,,发起者：密码资产管理系统，接收者：密码资产管理系统,系统检测到设备绑定完成且配置需要处理保护密钥,处理保护密钥,读取设备类型保护密钥配置,R,设备类型配置,是否需要创建保护密钥、是否需要同步密钥,1
,,,,,,,,创建保护密钥,W,保护密钥信息,密钥算法类型、密钥长度、密钥标识,1
,,,,,,,,同步保护密钥到密码设备,W,密钥同步信息,目标设备标识、密钥内容、同步时间,1
,,释放密码设备,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在密码设备管理页面点击释放设备按钮,解除密码设备与集群的绑定关系,输入待释放的密码设备信息,E,密码设备信息,设备ID、集群ID,1
,,,,,,,,读取密码设备当前绑定关系,R,设备绑定记录,设备ID、集群ID、绑定状态,1
,,,,,,,,解除密码设备与集群的绑定,W,设备绑定记录,设备ID、集群ID、绑定状态,1
,,,,,发起者：系统，接收者：密码资产数据管理系统,检测到密码设备集群最后一个设备释放请求,验证集群未被密码服务调用,读取密码服务调用状态,R,服务调用记录,集群ID、服务名称、调用状态,1
,,,,,,,,验证集群未被调用,X,验证结果,集群ID、是否可用,1
,,,,,,,,执行最终设备释放,W,设备释放记录,设备ID、集群ID、释放时间,1
,,云密码机列表,云密码机列表页面中，在搜索框中输入名称和管理IP，可以模糊查询云密码机列表,4.0,发起者：用户，接收者：密码资产数据管理系统-云密码机列表模块,用户在云密码机列表页面的搜索框输入名称和管理IP进行模糊查询,执行云密码机模糊查询,输入云密码机搜索条件,E,搜索条件,名称、管理IP,1
,,,,,,,,读取云密码机基础数据,R,云密码机信息,名称、管理IP、设备类型、状态、创建时间,1
,,,,,,,,输出模糊查询结果,X,云密码机列表,名称、管理IP、设备类型、状态、创建时间,1
,,云密码机新建,云密码机管理页面中，点击“新建”按钮，打开添加云密码机页面。输入云密码机信息，点击“确定”按钮添加云密码机。管理ip和管理端口根据实际设备部署情况获取,5.0,发起者：管理员，接收者：密码资产数据管理系统-云密码机管理模块,用户在云密码机管理页面点击“新建”按钮，输入云密码机信息并点击“确定”按钮,添加云密码机,输入云密码机信息,E,云密码机信息,云密码机名称、管理IP、管理端口,1
,,,,,,,,保存云密码机信息,W,云密码机信息,云密码机名称、管理IP、管理端口,1
,,云密码机编辑,云密码机信息列表，点击右侧操作列“编辑”按钮，打开编辑云密码机信息页面，修改云密码机名称和备注，点击“确定”按钮保存云密码机信息。,,发起者：用户，接收者：密码资产数据管理系统-云密码机管理模块,用户在云密码机信息列表中点击右侧操作列“编辑”按钮,加载云密码机编辑页面,读取云密码机基础信息,R,云密码机信息,云密码机ID、当前名称、当前备注,1
,,,,,,,,展示云密码机编辑页面,X,页面渲染数据,编辑表单字段（名称、备注）,1
,,,,,,用户在云密码机编辑页面点击“确定”按钮保存修改,保存云密码机信息,输入修改后的云密码机信息,E,云密码机信息,云密码机ID、修改后的名称、修改后的备注,1
,,,,,,,,校验云密码机信息,R,校验规则,名称格式规则、备注长度限制,1
,,,,,,,,更新云密码机信息,W,云密码机信息,云密码机ID、修改后的名称、修改后的备注,1
,,云密码机删除,云密码机列表页，点击右侧操作列 更多->“删除”按钮，在弹出框中点击“确定”按钮删除云密码机。系统中存在使用该云密码机生成的虚拟机时，云密码机不能删除。,3.0,发起者：用户，接收者：密码资产数据管理模块-云密码机删除功能,用户在云密码机列表页点击右侧操作列的‘更多->删除’按钮，并在弹出框中点击‘确定’按钮,删除云密码机,输入云密码机删除请求,E,云密码机ID,云密码机唯一标识符,1
,,,,,,,,读取云密码机信息,R,云密码机信息,云密码机名称、IP地址、状态、创建时间,1
,,,,,,,,删除云密码机,W,云密码机信息,云密码机名称、IP地址,1
,,,,,,用户在云密码机列表页点击右侧操作列的‘更多->删除’按钮,检查云密码机依赖,输入云密码机ID,E,云密码机ID,云密码机唯一标识符,1
,,,,,,,,查询依赖的虚拟机,R,虚拟机信息,虚拟机名称、虚拟机状态、关联云密码机ID,1
,,,,,,,,输出依赖检查结果,X,依赖状态,是否存在依赖虚拟机（布尔值）,1
,,云密码机详情,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5.0,发起者：用户，接收者：密码资产数据管理-云密码机详情模块,用户点击云密码机信息列表右侧操作列的“详情”按钮,查看云密码机详情信息,输入云密码机详情请求,E,请求参数,云密码机ID,1
,,,,,,,,读取云密码机详细信息,R,云密码机信息,云密码机名称、型号、IP地址、部署位置、状态、创建时间、更新时间,1
,,,,,,,,输出云密码机详情页面,X,详情页面数据,云密码机名称、型号、IP地址、部署位置、状态、创建时间、更新时间,1
,,网络配置列表,查看为云密码机配置的虚机网络,2.0,发起者：管理员，接收者：密码资产管理系统,管理员点击网络配置列表菜单,查看云密码机虚机网络配置,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取虚机网络配置数据,R,虚机网络配置信息,虚拟机名称、IP地址、子网、网关、VPC标识,1
,,,,,,,,输出虚机网络配置列表,X,虚机网络配置信息,虚拟机名称、IP地址、子网、网关、VPC标识,1
,,新增虚拟机网络配置,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6.0,发起者：管理员，接收者：密码资产数据管理系统,管理员点击虚拟机网络配置菜单,查看虚拟机网络配置信息,查询虚拟机网络配置分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取虚拟机网络配置信息,R,虚拟机网络配置,管理IP范围、业务IP范围、端口范围、创建时间,1
,,,,,,管理员在虚拟机网络配置页面点击新增配置按钮,新增虚拟机网络配置,输入管理IP范围,E,IP配置,起始管理IP、结束管理IP,1
,,,,,,,,输入业务IP范围,E,IP配置,起始业务IP、结束业务IP,1
,,,,,,,,输入端口范围,E,端口配置,起始端口、结束端口,1
,,,,,,,,验证IP/端口范围有效性,E,验证规则,IP格式校验、端口冲突检测,1
,,,,,,,,保存虚拟机网络配置,W,虚拟机网络配置,管理IP范围、业务IP范围、端口范围,1
,,批量创建虚拟机,批量创建虚拟密码机，自动加载虚机网络，支持配置虚机资源。调用云密码机0088标准创建虚机并自动配置网络。,10.0,发起者：管理员，接收者：密码资产管理系统,管理员在虚拟机管理页面点击批量创建按钮并输入创建参数,批量创建虚拟密码机,输入批量创建虚拟机参数,E,虚拟机创建参数,虚拟机数量、模板名称、CPU核心数、内存大小、存储容量,1
,,,,,,,,读取虚拟机模板信息,R,虚拟机模板,模板名称、操作系统类型、基础配置参数,1
,,,,,,,,生成虚拟机配置文件,W,虚拟机配置,虚拟机名称、IP地址、网络配置、资源分配,1
,,,,,,,,调用云密码机0088标准接口创建虚拟机,X,虚拟机创建请求,配置文件、认证令牌、API版本,1
,,,,,发起者：密码资产管理系统，接收者：网络配置模块,虚拟机创建成功后触发网络配置流程,自动配置虚拟机网络,读取网络模板信息,R,网络模板,网络名称、IP地址范围、子网掩码、网关,1
,,,,,,,,生成网络配置参数,W,网络配置,虚拟机IP地址、MAC地址、DNS配置,1
,,,,,,,,调用网络配置接口,X,网络配置请求,配置参数、认证信息、操作类型,1
,,,,,发起者：密码资产管理系统，接收者：资源管理模块,虚拟机创建成功后触发资源分配流程,配置虚拟机资源,读取资源模板信息,R,资源模板,CPU配额、内存配额、存储配额,1
,,,,,,,,生成资源分配参数,W,资源分配,虚拟机ID、CPU分配量、内存分配量、存储分配量,1
,,,,,,,,调用资源分配接口,X,资源分配请求,分配参数、认证信息、操作类型,1
,,虚拟密码机列表,平台中云机虚拟出的VSM,4.0,发起者：管理员，接收者：密码资产数据管理模块-虚拟密码机列表,管理员点击虚拟密码机列表菜单,查看虚拟密码机列表,查询分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取虚拟密码机数据,R,虚拟密码机信息,虚拟密码机名称、IP地址、端口、状态、创建时间,1
,,,,,,管理员在虚拟密码机列表页面点击新增按钮，创建虚拟密码机,新增虚拟密码机,输入虚拟密码机信息,E,虚拟密码机信息,虚拟密码机名称、IP地址、端口、配置参数,1
,,,,,,,,验证配置参数有效性,X,校验结果,校验状态、错误信息,1
,,,,,,,,保存虚拟密码机配置,W,虚拟密码机信息,虚拟密码机名称、IP地址、端口、配置参数,1
,,,,,,管理员在虚拟密码机列表页面点击编辑按钮，修改虚拟密码机信息,编辑虚拟密码机,选择待修改虚拟密码机,E,虚拟密码机标识,虚拟密码机ID,1
,,,,,,,,输入修改后的虚拟密码机信息,E,虚拟密码机信息,虚拟密码机名称、IP地址、端口、配置参数,1
,,,,,,,,验证修改后的配置参数有效性,X,校验结果,校验状态、错误信息,1
,,,,,,,,更新虚拟密码机配置,W,虚拟密码机信息,虚拟密码机ID、虚拟密码机名称、IP地址、端口、配置参数,1
,,,,,,管理员在虚拟密码机列表页面点击删除按钮，删除虚拟密码机,删除虚拟密码机,选择待删除虚拟密码机,E,虚拟密码机标识,虚拟密码机ID,1
,,,,,,,,确认删除操作,X,操作确认,确认状态,1
,,,,,,,,删除虚拟密码机记录,W,虚拟密码机信息,虚拟密码机ID,1
,,,,,,管理员在虚拟密码机列表页面点击详情按钮，查看虚拟密码机详细信息,查看虚拟密码机详情,选择待查看虚拟密码机,E,虚拟密码机标识,虚拟密码机ID,1
,,,,,,,,读取虚拟密码机详细信息,R,虚拟密码机信息,虚拟密码机名称、IP地址、端口、配置参数、状态、创建时间、更新时间,1
,,虚拟密码机列表查询,支持根据名称、主机、管理ip、服务ip、设备类型进行查询,2.0,发起者：用户，接收者：密码资产数据管理系统-虚拟密码机管理子模块,用户在虚拟密码机管理页面点击查询按钮或输入查询条件,查询虚拟密码机列表,输入查询条件,E,查询条件,名称、主机、管理IP、服务IP、设备类型,1
,,,,,,,,读取虚拟密码机信息,R,虚拟密码机信息,名称、主机、管理IP、服务IP、设备类型、状态、创建时间,1
,,,,,,,,输出查询结果,X,虚拟密码机列表,名称、主机、管理IP、服务IP、设备类型、状态、创建时间,1
,,创建虚拟密码机,选择云密码机，批量创建虚拟密码机，和云机管理中批量创建密码机一致,,发起者：管理员，接收者：密码资产管理系统,管理员在密码资产数据管理界面点击批量创建虚拟密码机按钮,批量创建虚拟密码机,选择云密码机类型,R,云密码机列表,云密码机名称、型号、规格、可用状态,1
,,,,,,,,输入创建参数,E,创建参数,创建数量、密码机配置信息（CPU/内存/存储）、网络配置,1
,,,,,,,,验证创建参数,R,配置规则,最大创建数量、最小配置要求、网络策略限制,1
,,,,,,,,创建虚拟密码机实例,W,虚拟密码机实例,实例ID、创建时间、配置详情、网络地址,1
,,,,,,,,返回创建结果,X,创建结果,成功数量、失败原因、实例列表,1
,,虚拟密码机详情,虚拟密码机详情,5.0,,管理员点击虚拟密码机详情菜单,查看虚拟密码机详细信息,输入虚拟密码机查询条件,E,查询条件,虚拟密码机ID,1
,,,,,,,,读取虚拟密码机基础信息,R,虚拟密码机信息,虚拟密码机名称、IP地址、端口号、所属集群、创建时间,1
,,,,,,,,读取虚拟密码机运行状态,R,运行状态信息,当前状态（运行/停用）、CPU使用率、内存占用、连接数,1
,,,,,,,,输出虚拟密码机详情,X,虚拟密码机详情,虚拟密码机名称、IP地址、端口号、所属集群、创建时间、当前状态、CPU使用率、内存占用、连接数,1
,,,,,,管理员在虚拟密码机详情页面点击配置修改按钮,配置虚拟密码机参数,输入虚拟密码机配置参数,E,配置参数,虚拟密码机名称、IP地址、端口号、所属集群,1
,,,,,,,,验证配置参数有效性,R,校验规则,IP地址格式、端口号范围、集群存在性,1
,,,,,,,,更新虚拟密码机配置,W,虚拟密码机信息,虚拟密码机名称、IP地址、端口号、所属集群,1
,,编辑虚拟密码机,编辑虚拟密码机名称、连接密码，并在动态下发给密码服务,4.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在虚拟密码机管理页面点击编辑按钮并填写新名称和连接密码后点击保存,编辑虚拟密码机信息并下发配置,读取待编辑的虚拟密码机原始信息,R,虚拟密码机基础信息,虚拟密码机ID、当前名称、当前连接密码,1
,,,,,,,,输入修改后的虚拟密码机名称和连接密码,E,虚拟密码机编辑信息,虚拟密码机ID、新名称、新连接密码,1
,,,,,,,,更新虚拟密码机配置信息,W,虚拟密码机配置表,虚拟密码机ID、名称、连接密码、更新时间,1
,,,,,,,,生成密码服务配置下发指令,X,密码服务配置指令,目标密码服务ID、虚拟密码机ID、连接参数,1
,,,,,,,,将配置下发至密码服务模块,X,密码服务接口,下发状态、错误代码、响应时间,1
,,删除虚拟密码机,删除虚拟密码机,3.0,,管理员在虚拟密码机管理页面点击删除按钮,删除虚拟密码机,输入虚拟密码机删除请求,E,虚拟密码机删除请求,虚拟密码机ID、删除确认标识,1
,,,,,,,,读取虚拟密码机配置信息,R,虚拟密码机配置信息,虚拟密码机ID、名称、IP地址、状态、关联资产列表,1
,,,,,,,,执行虚拟密码机删除操作,W,虚拟密码机状态信息,虚拟密码机ID、状态（已删除）,1
,,,,,,,,输出删除操作结果,X,操作结果信息,操作状态（成功/失败）、错误代码（如有）,1
,,启动虚拟密码机,启动虚拟密码机,,发起者：系统管理员，接收者：密码资产数据管理模块-虚拟密码机管理子模块,系统管理员在虚拟密码机管理页面点击启动按钮,启动虚拟密码机,输入启动虚拟密码机请求,E,启动请求信息,虚拟密码机ID、启动参数,1
,,,,,,,,验证虚拟密码机状态,R,虚拟密码机状态信息,当前状态（已停止/运行中）,1
,,,,,,,,执行虚拟密码机启动操作,W,启动指令,虚拟密码机ID、启动参数,1
,,,,,,,,返回启动结果,X,启动结果信息,状态码、错误信息,1
,,停止虚拟密码机,停止虚拟密码机,,发起者：管理员，接收者：密码资产数据管理系统,管理员在虚拟密码机管理页面点击停止按钮,停止虚拟密码机,验证管理员权限,R,用户权限信息,用户ID、权限级别,1
,,,,,,,,输入停止虚拟密码机指令,E,操作指令,操作类型（停止）、虚拟密码机ID,1
,,,,,,,,更新虚拟密码机状态,W,虚拟密码机状态,虚拟密码机ID、状态（停止中）,1
,,,,,,,,记录操作日志,W,系统日志,操作用户、操作时间、操作类型、虚拟密码机ID、操作结果,1
,,重启虚拟密码机,重启虚拟密码机,,发起者：管理员，接收者：密码资产管理系统-虚拟密码机管理模块,管理员在虚拟密码机管理页面点击重启按钮,确认重启虚拟密码机,输入重启请求信息,E,重启请求信息,虚拟密码机实例ID、重启原因,1
,,,,,,,,读取虚拟密码机当前状态,R,虚拟密码机状态,当前运行状态、配置信息,1
,,,,,,,,验证用户重启权限,R,用户权限信息,用户角色、操作权限,1
,,,,,,,,记录重启操作日志,W,操作日志,操作类型、操作时间、操作用户,1
,,,,,,管理员在虚拟密码机管理页面点击确认重启按钮,执行重启虚拟密码机,发送重启确认指令,E,确认信息,确认状态、确认时间,1
,,,,,,,,执行虚拟密码机重启命令,X,重启命令,命令类型、目标实例,1
,,,,,,,,读取重启后虚拟密码机状态,R,虚拟密码机状态,重启后运行状态、配置信息,1
,,,,,,,,更新虚拟密码机状态记录,W,虚拟密码机状态,最新运行状态、重启时间,1
,,,,,,,,记录重启完成日志,W,操作日志,操作结果、完成时间,1
,,强制删除虚拟密码机,解决虚拟密码机已不存在，无法正常删除情况,,发起者：系统管理员，接收者：密码资产管理系统,系统管理员在密码资产列表中点击强制删除虚拟密码机按钮,强制删除虚拟密码机,输入虚拟密码机标识信息,E,虚拟密码机标识,虚拟密码机ID、虚拟密码机名称,1
,,,,,,,,验证虚拟密码机状态,R,虚拟密码机状态信息,虚拟密码机ID、当前状态（已失效/不存在）,1
,,,,,,,,执行强制删除操作,W,虚拟密码机记录,虚拟密码机ID、删除标记、删除时间,1
,,,,,,,,输出删除结果,X,操作结果,操作状态（成功/失败）、错误信息,1
,,生成虚机影像,生成虚机影像,4.0,发起者：系统管理员，接收者：密码资产数据管理模块,系统管理员在密码资产数据管理界面点击生成虚机影像按钮,生成虚机影像,输入虚机影像生成参数,E,虚机影像参数,影像名称、存储路径、模板类型、加密配置,1
,,,,,,,,读取密码资产模板配置,R,密码资产模板,模板名称、加密算法、密钥长度、存储策略,1
,,,,,,,,生成虚机影像文件,X,生成结果,影像文件路径、生成状态、错误信息,1
,,,,,,,,写入虚机影像元数据,W,影像元数据,影像ID、生成时间、关联密码资产、存储位置,1
,,,,,发起者：自动化任务调度器，接收者：密码资产数据管理模块,定时任务触发虚机影像生成,批量生成虚机影像,读取批量生成配置,R,批量配置,任务名称、影像数量、模板列表、资源池,1
,,,,,,,,执行并行影像生成,X,生成状态,成功数量、失败数量、错误日志,1
,,,,,,,,记录批量任务日志,W,任务日志,任务ID、开始时间、结束时间、资源消耗,1
,,下载虚机影像,下载虚机影像,,发起者：用户，接收者：密码资产数据管理模块-虚拟机影像管理子模块,用户在虚拟机影像管理页面点击下载按钮，选择要下载的虚拟机影像,下载虚拟机影像,选择虚拟机影像,E,虚拟机影像选择信息,影像ID、版本,1
,,,,,,,,读取虚拟机影像信息,R,虚拟机影像信息,名称、路径、大小,1
,,,,,,,,验证用户下载权限,R,用户权限信息,用户ID、权限类型,1
,,,,,,,,下载虚拟机影像文件,X,虚拟机影像文件,文件路径、大小,1
,,,,,,,,记录下载操作,W,下载记录,用户ID、影像ID、下载时间,1
,,导入虚机影像,导入虚机影像，还原虚机影像,,发起者：用户，接收者：密码资产数据管理模块-导入虚机影像模块,用户点击导入虚机影像按钮并选择影像文件,导入虚机影像,输入虚机影像文件信息,E,虚机影像文件信息,文件名、文件路径、文件格式、文件大小,1
,,,,,,,,验证虚机影像文件格式和完整性,R,文件元数据,文件头信息、校验码、文件扩展名,1
,,,,,,,,保存虚机影像文件到指定存储位置,W,存储位置信息,存储路径、文件名、存储格式,1
,,,,,,,,输出导入结果状态,X,操作结果,成功/失败标识、错误代码、错误描述,1
,,,,,,用户点击还原虚机影像按钮并选择目标环境,还原虚机影像,选择待还原的虚机影像文件,E,虚机影像列表,文件名、存储路径、创建时间,1
,,,,,,,,验证虚机影像文件可用性,R,文件状态信息,文件完整性、存储权限、依赖项状态,1
,,,,,,,,执行虚机影像还原操作,W,还原配置参数,目标环境配置、还原路径、资源分配参数,1
,,,,,,,,输出还原操作结果,X,操作结果,成功/失败标识、错误代码、错误描述,1
,,物理密码机列表,物理密码机列表展示已经注册到系统中物理密码机信息，包含密码机的名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注等信息。,,发起者：管理员，接收者：密码资产数据管理系统,管理员点击物理密码机列表菜单,查看物理密码机列表信息,查询物理密码机分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取物理密码机基础信息,R,物理密码机信息,密码机名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号,1
,,,,,,,,读取物理密码机扩展信息,R,物理密码机信息,完整性校验状态、备注,1
,,,,,,管理员在物理密码机列表页面输入搜索条件,搜索物理密码机信息,输入搜索条件,E,搜索条件,密码机名称、所属厂商、设备类型、管理IP,1
,,,,,,,,读取匹配的物理密码机信息,R,物理密码机信息,密码机名称、所属厂商、设备类型、管理IP、版本、序列号,1
,,物理密码机新建,系统密码机是将机房已经上架部署完毕的密码机注册到系统中，交由管理平台进行统一管理。,5.0,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机注册按钮，进入密码机注册页面,注册物理密码机,输入密码机基本信息,E,密码机基础信息,密码机名称、IP地址、端口号、部署位置、所属部门,1
,,,,,,,,验证密码机连接状态,R,密码机状态信息,连接状态、响应时间、协议版本,1
,,,,,,,,保存密码机配置信息,W,密码机配置表,密码机名称、IP地址、端口号、部署位置、所属部门、注册时间,1
,,,,,,,,返回注册结果,X,操作结果,注册状态（成功/失败）、错误代码、提示信息,1
,,,,,,管理员在密码机注册页面点击查询按钮,查询物理密码机信息,输入查询条件,E,查询条件,密码机名称、IP地址、部署位置,1
,,,,,,,,读取密码机信息,R,密码机信息列表,密码机名称、IP地址、端口号、部署位置、所属部门、注册时间,1
,,,,,,,,返回查询结果,X,查询结果,密码机信息列表、总记录数,1
,,物理密码机编辑,在密码机列表页，点击右侧操作列“编辑”按钮，打开物理密码机编辑页面，修改物理密码机信息(可编辑名称、备注、连接密码)后，点击“确定”按钮保存编辑的信息,3.0,发起者：管理员，接收者：密码资产数据管理系统-物理密码机模块,管理员在密码机列表页点击右侧操作列“编辑”按钮,查看物理密码机编辑页面,读取物理密码机基础信息,R,物理密码机信息,名称、备注、连接密码,1
,,,,,,管理员在物理密码机编辑页面点击“确定”按钮保存修改,保存物理密码机编辑信息,输入修改后的物理密码机信息,E,物理密码机信息,名称、备注、连接密码,1
,,,,,,,,验证输入数据有效性,R,数据校验规则,字段格式约束、唯一性校验规则,1
,,,,,,,,更新物理密码机信息,W,物理密码机信息,名称、备注、连接密码,1
,,物理密码机删除,在密码机信息列表中可以删除已经注册到系统中的设备信息。,,发起者：管理员，接收者：密码资产管理系统,管理员在密码机信息列表中点击删除按钮,删除物理密码机信息,输入删除请求,E,删除请求参数,设备ID、操作用户ID,1
,,,,,,,,读取设备信息,R,物理密码机信息,设备ID、设备名称、注册时间、状态,1
,,,,,,,,验证删除权限,R,用户权限信息,用户角色、操作权限,1
,,,,,,,,执行删除操作,W,物理密码机信息,设备ID,1
,,,,,,,,输出删除结果,X,操作结果,删除状态、错误信息,1
,,物理密码机详情,在密码机列表页，点击右侧操作列“详情”按钮，系统打开密码机详情页面。,5.0,发起者：用户，接收者：密码资产数据管理系统-物理密码机详情模块,用户在密码机列表页面点击右侧操作列的‘详情’按钮,查看物理密码机详情信息,输入密码机详情请求,E,密码机详情请求,密码机ID,1
,,,,,,,,读取物理密码机详情信息,R,物理密码机详情信息,密码机名称、型号、IP地址、端口号、部署位置、状态、证书有效期、密钥类型、关联业务系统,1
,,,,,,,,输出物理密码机详情信息,X,物理密码机详情信息,密码机名称、型号、IP地址、端口号、部署位置、状态、证书有效期、密钥类型、关联业务系统,1
,,强制删除,解决物理密码机已损坏，无法正常删除情况,3.0,发起者：系统管理员，接收者：密码资产数据管理模块,系统管理员在密码资产列表中点击强制删除按钮,验证强制删除请求,读取管理员身份信息,R,用户权限信息,用户ID、权限级别,1
,,,,,,,,校验删除权限,E,权限校验规则,操作类型、权限阈值,1
,,,,,,系统管理员确认强制删除操作,执行强制删除操作,标记密码资产为已删除,W,密码资产状态,资产ID、删除状态、删除时间,1
,,,,,,,,清除物理密码机残留数据,W,物理设备操作指令,设备ID、操作类型、加密算法,1
,,,,,发起者：密码资产数据管理模块，接收者：审计日志系统,强制删除操作完成,记录强制删除日志,生成删除操作日志,W,系统操作日志,操作时间、操作用户、资产ID、操作结果,1
,,管理页面跳转,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,,发起者：用户，接收者：密码资产数据管理系统,用户在密码资产列表中点击设备管理页面跳转按钮,跳转到设备管理页面,获取当前设备标识信息,E,设备操作请求,设备ID、设备类型,1
,,,,,,,,读取设备管理页面配置,R,设备管理页面配置,设备类型、管理页面URL模板,1
,,,,,,,,生成完整的管理页面地址,X,页面跳转指令,完整URL地址,1
,,保护主密钥同步,支持设备内保护主密钥的同步,4.0,发起者：系统管理员，接收者：密码资产数据管理模块,系统管理员在密码资产管理界面点击主密钥同步按钮,查询设备主密钥信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取设备列表,R,设备信息,设备ID、设备名称、设备类型,1
,,,,,,,,读取设备主密钥信息,R,主密钥信息,密钥ID、算法类型、创建时间、状态,1
,,,,,,系统管理员在设备主密钥详情页面点击同步按钮,同步设备主密钥,选择目标设备,E,设备信息,设备ID、设备名称,1
,,,,,,,,输入同步参数,E,同步配置,同步方式（全量/增量）、同步时间,1
,,,,,,,,验证目标设备密钥状态,R,主密钥状态,密钥版本、同步时间戳,1
,,,,,,,,执行密钥同步操作,W,主密钥同步记录,源设备ID、目标设备ID、同步时间、同步状态,1
,,保护主密钥备份,将设备内保护主密钥的加密导出备份,3.0,发起者：系统管理员，接收者：密码资产管理系统-密钥备份模块,系统管理员在密钥管理界面点击主密钥备份按钮,执行主密钥加密备份,输入主密钥备份参数,E,备份参数,加密算法、加密密钥,1
,,,,,,,,读取设备内主密钥信息,R,主密钥信息,主密钥ID、主密钥内容,1
,,,,,,,,加密主密钥并生成备份文件,W,加密后的主密钥,加密数据、加密时间戳,1
,,,,,,,,输出备份完成状态,X,备份状态,备份成功/失败信息,1
,,保护主密钥还原,还原设备内保护主密钥,,发起者：系统管理员，接收者：密码资产管理系统,系统管理员在密钥管理界面点击主密钥还原按钮并选择备份文件,验证主密钥还原请求,输入用户身份凭证,E,用户凭证,用户名、密码,1
,,,,,,,,读取用户权限信息,R,用户权限表,用户ID、操作权限,1
,,,,,,,,校验还原操作权限,X,权限校验结果,是否允许操作,1
,,,,,,系统管理员确认主密钥还原操作,执行主密钥还原,读取备份密钥文件,R,密钥备份文件,加密主密钥、时间戳、签名,1
,,,,,,,,验证备份文件完整性,X,校验结果,是否完整、是否被篡改,1
,,,,,,,,解密主密钥数据,E,解密参数,解密算法、密钥,1
,,,,,,,,写入设备主密钥,W,设备密钥存储,设备ID、主密钥值,1
,,,,,,,,更新密钥状态记录,W,密钥状态表,密钥ID、还原时间、操作人,1
,,,,,发起者：密码资产管理系统，接收者：系统管理员,主密钥还原操作完成,反馈还原结果,输出操作结果状态,X,操作结果,成功/失败、错误代码,1
,,,,,,,,生成还原操作日志,W,系统日志,操作时间、操作人、设备ID、结果,1
,密码产品证书及编号管理,用户证书导入,导出用户证书，支持传入签名证书和加密证书。,6.0,发起者：管理员，接收者：密码资产数据管理系统-证书管理模块,管理员在证书管理页面点击导出用户证书按钮,导出用户证书,输入导出参数,E,导出参数,证书类型（签名/加密）、导出格式（PEM/DER）,1
,,,,,,,,读取用户证书数据,R,用户证书信息,证书内容、证书类型、有效期、颁发机构,1
,,,,,,,,生成证书文件,X,证书文件,文件内容（Base64编码）、文件格式、证书类型标识,1
,,,,,,管理员在证书管理页面选择证书类型并确认导出,验证证书导出权限,读取用户权限配置,R,用户权限信息,用户ID、操作权限（导出）、证书类型权限,1
,,,,,,,,校验导出操作合法性,E,校验规则,证书类型匹配规则、权限匹配规则,1
,,用户证书列表,列表分页展示用户证书,3.0,发起者：用户，接收者：密码资产数据管理系统-密码产品证书及编号管理模块,用户点击用户证书列表菜单,查看用户证书列表,读取用户证书信息,R,用户证书信息,证书名称、颁发机构、有效期、证书状态,1
,,,,,,用户在用户证书列表页面点击分页按钮,分页查询用户证书,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页用户证书信息,R,用户证书信息,证书名称、颁发机构、有效期、证书状态,1
,,,,,,,,输出分页用户证书列表,X,分页用户证书列表,证书名称、颁发机构、有效期、证书状态,1
,,用户证书停用,停用用户证书,,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击停用按钮,停用用户证书,输入停用请求,E,停用请求信息,证书ID、用户ID、停用原因,1
,,,,,,,,读取用户证书信息,R,用户证书信息,证书ID、用户ID、当前状态,1
,,,,,,,,更新证书状态为停用,W,用户证书信息,证书ID、用户ID、新状态（停用）,1
,,,,,,,,输出操作结果,X,操作结果,操作状态（成功/失败）、错误信息,1
,,用户证书启用,启用用户证书,,发起者：管理员，接收者：密码资产数据管理系统-证书管理模块,管理员在证书管理页面点击证书启用按钮，启用指定用户证书,启用用户证书,验证用户权限,R,用户权限信息,用户ID、权限等级,1
,,,,,,,,输入证书启用信息,E,证书信息,证书ID、用户ID、启用状态,1
,,,,,,,,更新证书状态,W,证书状态信息,证书ID、启用状态、更新时间,1
,,,,,,,,返回启用结果,X,操作结果,操作状态、错误信息,1
,,用户证书删除,删除用户证书,,,管理员在用户证书管理页面点击删除按钮，选择用户证书进行删除,删除用户证书,输入待删除的用户证书信息,E,用户证书信息,证书ID、用户ID、证书状态,1
,,,,,,,,从证书数据库中删除用户证书记录,W,用户证书信息,证书ID、用户ID、证书状态,1
,,,,,,,,输出证书删除操作结果,X,操作结果信息,操作状态（成功/失败）、错误代码（如有）,1
,,应用证书创建,创建应用证书,5.0,,管理员在证书管理页面点击创建应用证书按钮,创建应用证书,输入证书基本信息,E,证书信息,证书名称、有效期、密钥类型、证书用途,1
,,,,,,,,验证证书信息有效性,R,系统配置规则,密钥类型限制、有效期范围、证书用途分类,1
,,,,,,,,调用证书生成服务生成证书,X,证书生成请求,证书名称、密钥类型、有效期、证书用途,1
,,,,,,,,保存生成的证书信息,W,证书存储记录,证书名称、证书内容、生成时间、关联应用,1
,,,,,,,,返回证书生成结果,X,操作结果,生成状态、证书下载链接、错误信息,1
,,下载应用证书证书请求,下载应用证书证书请求,3.0,发起者：用户，接收者：密码资产数据管理系统,用户点击下载应用证书证书请求按钮,生成应用证书证书请求文件,验证用户下载权限,R,用户权限信息,用户ID、操作权限,1
,,,,,,,,读取证书请求模板,R,证书请求模板,证书类型、字段格式,1
,,,,,,,,输入用户证书请求参数,E,证书请求参数,证书名称、有效期、密钥算法,1
,,,,,,,,生成证书请求文件,W,证书请求文件,PEM格式、CSR内容,1
,,,,,,,,输出证书请求文件,X,下载文件,文件名、文件类型,1
,,,,,,用户在证书请求管理页面点击下载历史请求,下载历史证书请求文件,查询历史请求记录,E,分页信息,页码、单页数量,1
,,,,,,,,读取历史请求文件,R,证书请求记录,请求ID、生成时间、文件存储路径,1
,,,,,,,,输出历史请求文件,X,下载文件,文件名、文件类型,1
,,应用证书导入,根据证书请求导入应用证书,,发起者：用户，接收者：密码资产数据管理模块-证书管理模块,用户在证书管理页面点击导入证书按钮，选择证书请求文件,查看证书请求信息,查询证书请求信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取证书请求信息,R,证书请求信息,证书名称、请求内容、请求格式、文件大小,1
,,,,,,用户在证书请求信息页面点击导入按钮，确认证书导入,导入应用证书,输入证书信息,E,证书信息,证书名称、请求内容、请求格式、有效期、签发机构,1
,,,,,,,,验证证书格式,X,验证结果,格式校验状态、错误信息,1
,,,,,,,,保存应用证书,W,应用证书信息,证书名称、请求内容、请求格式、有效期、签发机构,1
,,导入应用证书和密钥,直接导入应用证书，包括签名证书、证书口令、加密证书、加密私钥,,发起者：管理员，接收者：密码资产数据管理系统,管理员点击证书导入按钮并上传文件,导入应用证书和密钥,读取上传的证书文件,E,证书文件,签名证书、证书口令、加密证书、加密私钥,1
,,,,,,,,验证证书格式有效性,R,证书校验规则,证书类型、加密算法、有效期,1
,,,,,,,,校验证书口令匹配性,R,证书口令信息,加密证书、私钥口令,1
,,,,,,,,存储证书到密钥库,W,密码资产数据库,证书类型、证书内容、密钥类型,1
,,,,,,,,生成证书导入记录,W,操作日志,操作人、操作时间、证书类型,1
,,,,,发起者：系统，接收者：密码资产数据管理系统,证书导入任务完成,通知导入结果,输出证书导入状态,X,操作反馈,成功/失败状态、错误代码,1
,,应用证书列表查询,分页展示应用证书列表,,发起者：用户，接收者：密码资产数据管理-密码产品证书及编号管理模块,用户点击应用证书列表查询菜单,查看应用证书列表信息,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取应用证书列表数据,R,应用证书信息,证书名称、证书编号、颁发机构、有效期、状态,1
,,应用证书停用,停用应用证书,,发起者：管理员，接收者：密码资产数据管理系统-密码产品证书及编号管理模块,管理员在应用证书管理页面点击停用按钮，选择要停用的应用证书,查看应用证书停用信息,查询应用证书信息,E,查询条件,证书ID、证书名称,1
,,,,,,,,读取应用证书状态,R,证书信息,证书ID、证书名称、当前状态、有效期,1
,,,,,,,,显示停用证书界面,X,证书详情,证书ID、证书名称、当前状态、停用原因输入框,1
,,,,,,管理员在停用证书页面点击确认停用按钮,执行应用证书停用操作,输入停用原因,E,停用信息,证书ID、停用原因,1
,,,,,,,,验证证书有效性,R,证书状态,证书ID、当前状态,1
,,,,,,,,更新证书状态为停用,W,证书状态,证书ID、新状态,1
,,,,,,,,记录停用操作日志,W,操作日志,操作人、操作时间、证书ID、操作类型、停用原因,1
,,,,,,,,返回停用结果,X,操作结果,证书ID、操作状态、提示信息,1
,,应用证书启用,启用应用证书,,发起者：管理员，接收者：密码资产管理系统,管理员在证书管理页面点击启用按钮，启用指定应用证书,启用应用证书,验证证书状态,R,证书信息,证书ID、当前状态,1
,,,,,,,,输入启用请求,E,启用参数,证书ID、启用状态、操作用户,1
,,,,,,,,更新证书状态,W,证书信息,证书ID、启用状态、更新时间,1
,,,,,,,,返回启用结果,X,操作结果,证书ID、操作状态、错误信息,1
,,,,,发起者：系统，接收者：密码资产管理系统,系统定时任务检测证书状态变更,记录证书启用日志,读取启用事件数据,R,操作日志,证书ID、操作类型、操作时间,1
,,,,,,,,写入审计日志,W,审计日志,证书ID、操作类型、操作时间、操作用户,1
,,应用证书删除,删除应用证书,,发起者：管理员，接收者：密码资产数据管理系统-证书管理模块,管理员在证书管理页面点击删除按钮并确认删除操作,确认应用证书删除请求,读取待删除证书信息,R,证书信息,证书ID、证书名称、证书状态,1
,,,,,,,,验证删除权限,R,用户权限信息,管理员ID、操作权限,1
,,,,,,,,输出删除确认提示,X,操作确认信息,证书名称、删除警告,1
,,,,,,管理员在删除确认页面点击最终删除按钮,执行应用证书删除操作,输入删除请求参数,E,删除请求,证书ID、管理员ID、删除原因,1
,,,,,,,,更新证书状态为已删除,W,证书状态记录,证书ID、删除时间、操作者,1
,,,,,,,,输出删除结果,X,操作结果,证书名称、删除状态、操作时间,1
,密钥信息管理,新增密钥,新增密钥，支持创建对称密钥和非对称密钥。支持3DES、AES、SM4、SM2、SM9、RSA、ZUC等密钥算法,10.0,发起者：管理员，接收者：密码资产管理系统-密钥管理模块,管理员在密钥管理页面点击新增密钥按钮,创建对称密钥,输入对称密钥基本信息,E,密钥基本信息,密钥名称、算法类型（3DES/AES/SM4/ZUC）、密钥长度,1
,,,,,,,,生成对称密钥,X,生成的密钥数据,密钥值（Base64编码）,1
,,,,,,,,保存对称密钥信息,W,密钥存储记录,密钥名称、算法类型、密钥值、创建时间、密钥状态,1
,,,,,,,创建非对称密钥,输入非对称密钥基本信息,E,密钥基本信息,密钥名称、算法类型（SM2/SM9/RSA）、密钥长度,1
,,,,,,,,生成非对称密钥对,X,生成的密钥对数据,公钥值（PEM格式）、私钥值（PEM格式）,1
,,,,,,,,保存非对称密钥信息,W,密钥存储记录,密钥名称、算法类型、公钥值、私钥值、创建时间、密钥状态,1
,,密钥信息列表,列表分页展示密钥信息，包括应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度等信息。,4.0,发起者：用户，接收者：密码资产数据管理系统-密钥信息管理模块,用户点击密钥信息列表菜单,查看密钥信息列表,读取密钥信息,R,密钥信息,应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度,1
,,,,,,用户在密钥信息列表页面点击分页按钮,分页查询密钥信息,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页密钥信息,R,密钥信息,应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度,1
,,密钥查询,根据应用、密钥id、密钥名称查询密钥,2.0,发起者：用户，接收者：密码资产数据管理-密钥信息管理模块,用户在密钥查询页面点击查询按钮，输入应用、密钥ID、密钥名称进行查询,查询密钥信息,输入查询条件,E,查询条件,应用、密钥ID、密钥名称,1
,,,,,,,,读取密钥信息,R,密钥信息,密钥ID、密钥名称、应用、密钥类型、创建时间、有效期、状态,1
,,,,,,,,输出查询结果,X,查询结果,密钥ID、密钥名称、应用、密钥类型、创建时间、有效期、状态,1
,,密钥详情,展示密钥详情信息，包括密钥摘要值，密钥来源、密钥用途、是否可导出等,4.0,发起者：用户，接收者：密码资产数据管理系统-密钥信息管理模块,用户点击密钥详情菜单,查看密钥详情信息,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密钥详情信息,R,密钥详情信息,密钥摘要值、密钥来源、密钥用途、是否可导出,1
,,密钥链接,查看密钥的密钥链接信息,3.0,发起者：用户，接收者：密码资产数据管理-密钥信息管理模块,用户点击密钥链接信息查看按钮,查看密钥链接信息,输入密钥链接查询条件,E,查询条件,密钥ID、密钥名称,1
,,,,,,,,读取密钥链接信息,R,密钥链接信息,链接名称、链接地址、创建时间、关联密钥ID,1
,,,,,,,,输出密钥链接信息列表,X,密钥链接信息列表,链接名称、链接地址、创建时间、关联密钥ID,1
,,密钥历史版本,支持查看密钥的历史版本信息,,发起者：管理员，接收者：密码资产数据管理系统,管理员在密钥信息管理页面点击历史版本查看按钮,查看密钥历史版本信息,输入密钥历史版本查询条件,E,查询条件,密钥ID、页码、单页数量,1
,,,,,,,,读取密钥历史版本数据,R,密钥历史版本信息,密钥ID、版本号、创建时间、操作人、密钥内容哈希值,1
,,,,,,,,输出密钥历史版本列表,X,分页结果,总记录数、历史版本列表（包含版本号、创建时间、操作人）,1
,,密钥翻新,将密钥做翻新处理,4.0,发起者：密钥管理员，接收者：密码资产数据管理系统,密钥管理员在密钥管理界面点击密钥翻新按钮,发起密钥翻新操作,选择待翻新的密钥,E,密钥信息,密钥ID、密钥名称、密钥类型,1
,,,,,,,,输入翻新参数,E,翻新参数,新密钥长度、加密算法、有效期限,1
,,,,,,,,读取原始密钥信息,R,密钥信息,密钥ID、旧密钥值、创建时间,1
,,,,,发起者：密码资产数据管理系统，接收者：密码资产数据管理系统,系统接收到密钥翻新请求,执行密钥翻新处理,生成新密钥值,W,密钥信息,密钥ID、新密钥值、生成时间,1
,,,,,,,,更新密钥状态,W,密钥信息,密钥ID、密钥状态（激活/停用）,1
,,,,,,,,记录翻新操作日志,W,操作日志,操作时间、操作用户、操作类型、密钥ID,1
,,,,,发起者：密码资产数据管理系统，接收者：密钥管理员,密钥翻新处理完成,返回翻新结果,输出翻新结果状态,X,操作结果,密钥ID、翻新状态（成功/失败）、错误信息,1
,,,,,,,,展示新密钥信息,X,密钥信息,密钥ID、新密钥值、有效期限,1
,,密钥自动翻新,根据自动翻新策略，自动翻新密钥,,发起者：系统调度器，接收者：密码资产数据管理系统-密钥信息管理模块,系统根据预设的自动翻新策略触发密钥翻新流程,执行密钥自动翻新,查询符合翻新条件的密钥,R,密钥信息,密钥ID、密钥类型、创建时间、有效期、使用状态,1
,,,,,,,,生成新密钥,E,密钥生成参数,密钥算法、密钥长度、加密强度,1
,,,,,,,,更新密钥存储信息,W,密钥信息,密钥ID、新密钥值、更新时间、版本号,1
,,,,,,,,记录密钥翻新操作日志,W,操作日志,操作时间、操作类型、密钥ID、操作结果,1
,,,,,发起者：系统监控模块，接收者：密码资产数据管理系统-密钥信息管理模块,系统检测到密钥翻新任务执行异常,处理密钥翻新异常,读取异常任务信息,R,任务状态信息,任务ID、异常类型、错误代码、发生时间,1
,,,,,,,,生成异常处理记录,W,异常处理日志,处理时间、异常描述、处理结果,1
,,密钥归档,将密钥做归档处理,3.0,发起者：管理员，接收者：密码资产数据管理系统-密钥信息管理模块,管理员在密钥管理页面点击归档按钮，选择密钥进行归档操作,执行密钥归档处理,选择待归档密钥,E,密钥选择信息,密钥ID、密钥名称、密钥状态,1
,,,,,,,,读取密钥元数据,R,密钥元数据,密钥ID、创建时间、使用次数、关联系统,1
,,,,,,,,更新密钥状态为归档,W,密钥状态记录,密钥ID、原状态、新状态（归档）、操作时间,1
,,,,,,,,生成归档操作日志,W,操作日志,操作类型（归档）、操作人、操作时间、影响密钥ID列表,1
,,,,,,,,返回归档结果,X,归档响应,成功/失败状态、归档密钥数量、错误信息（如有）,1
,,密钥恢复,将密钥做归档恢复,,发起者：管理员，接收者：密码资产管理系统,管理员在密钥管理界面点击密钥恢复按钮,恢复归档密钥,选择归档密钥,E,归档密钥信息,密钥ID、归档时间、密钥类型,1
,,,,,,,,验证恢复条件,R,密钥状态信息,当前状态、关联业务系统状态,1
,,,,,,,,执行密钥恢复,W,密钥状态变更,密钥ID、新状态、恢复时间,1
,,,,,,,,生成恢复日志,W,操作日志,操作类型、操作人、操作时间、密钥ID,1
,,密钥注销,将密钥做注销处理,,,管理员在密钥管理界面点击密钥注销按钮,查看待注销密钥信息,输入密钥查询条件,E,密钥查询条件,密钥ID、密钥名称、密钥类型,1
,,,,,,,,读取密钥基本信息,R,密钥信息,密钥ID、密钥名称、密钥类型、创建时间、当前状态,1
,,,,,,管理员在密钥详情页面点击注销操作按钮,执行密钥注销操作,输入密钥注销请求,E,注销请求,密钥ID、注销原因、操作人,1
,,,,,,,,验证密钥状态,R,密钥状态,当前状态、关联业务系统状态,1
,,,,,,,,更新密钥状态为已注销,W,密钥状态,密钥ID、新状态、注销时间,1
,,,,,,,,记录注销操作日志,W,操作日志,操作类型、操作人、操作时间、密钥ID、注销原因,1
,,,,,,,,输出注销结果,X,操作结果,操作状态、错误信息（如有）,1
,,密钥销毁,将密钥做销毁处理,,发起者：管理员，接收者：密码资产管理系统-密钥信息管理模块,管理员在密钥管理页面点击密钥销毁按钮，输入密钥销毁申请,申请密钥销毁,输入密钥销毁申请,E,密钥销毁申请信息,密钥ID、销毁原因、申请时间,1
,,,,,,,,读取密钥基本信息,R,密钥元数据,密钥ID、密钥类型、创建时间、使用状态,1
,,,,,,,,验证密钥可销毁状态,R,密钥状态校验规则,密钥使用状态、关联业务状态,1
,,,,,,,,写入密钥销毁请求,W,待销毁密钥队列,密钥ID、销毁原因、申请时间、申请者,1
,,,,,发起者：系统定时任务，接收者：密码资产管理系统-密钥信息管理模块,系统检测到待销毁密钥队列存在未处理记录,执行密钥销毁,读取待销毁密钥记录,R,待销毁密钥队列,密钥ID、销毁原因、申请时间,1
,,,,,,,,执行密钥数据销毁,W,密钥存储数据,密钥明文数据、加密密钥、存储路径,1
,,,,,,,,更新密钥状态为已销毁,W,密钥元数据,密钥ID、当前状态、销毁时间,1
,,,,,,,,记录销毁操作日志,W,密钥操作日志,操作类型、密钥ID、操作时间、操作结果,1
,,,,,发起者：管理员，接收者：密码资产管理系统-密钥信息管理模块,管理员在销毁记录页面输入查询条件,查看密钥销毁记录,输入销毁记录查询条件,E,查询条件,时间范围、密钥ID、操作者,1
,,,,,,,,读取密钥销毁记录,R,密钥操作日志,操作类型、密钥ID、操作时间、操作者、操作结果,1
,,,,,,,,输出销毁记录列表,X,销毁记录列表,密钥ID、销毁时间、操作者、操作结果,1
,,密钥删除,将密钥做删除处理,,发起者：管理员，接收者：密码资产管理系统-密钥管理模块,管理员在密钥管理页面点击删除按钮，选择要删除的密钥,查看待删除密钥信息,查询密钥信息,E,查询条件,密钥ID、密钥名称,1
,,,,,,,,读取密钥详情,R,密钥信息,密钥ID、密钥名称、创建时间、使用状态、关联资产,1
,,,,,,,执行密钥删除,输入删除请求,E,删除请求,密钥ID、删除原因,1
,,,,,,,,验证密钥存在性,R,密钥信息,密钥ID、使用状态,1
,,,,,,,,执行密钥删除,W,密钥状态,密钥ID、删除状态、删除时间,1
,,,,,,,,返回删除结果,X,操作结果,操作状态、错误信息,1
,密码文档信息管理,添加密码知识库数据,平台操作员登录，可上传文件添加一条知识库记录,4.0,发起者：平台操作员，接收者：密码资产数据管理模块,操作员在知识库管理页面点击上传文件按钮，上传文件并添加知识库记录,添加密码知识库记录,输入文件信息,E,文件信息,文件名、文件类型、上传者、上传时间,1
,,,,,,,,读取文件内容,R,文件内容,文件数据、文件大小,1
,,,,,,,,保存知识库记录,W,知识库记录,文件名、文件类型、上传者、上传时间、文件数据,1
,,,,,,,,输出添加结果,X,操作结果,添加成功/失败信息,1
,,编辑密码知识库数据,平台操作员登录，可编辑知识库记录,3.0,发起者：平台操作员，接收者：密码资产数据管理系统,平台操作员在知识库管理页面点击编辑按钮,编辑知识库记录,输入知识库编辑内容,E,知识库编辑信息,知识库ID、标题、内容、标签、版本号,1
,,,,,,,,读取原始知识库记录,R,知识库记录,知识库ID、标题、内容、标签、创建时间、修改时间,1
,,,,,,,,验证编辑内容格式,X,验证结果,格式校验状态、错误信息,1
,,,,,,平台操作员在知识库编辑页面点击保存按钮,保存知识库记录,输入保存请求参数,E,保存请求,知识库ID、操作员ID、操作时间戳,1
,,,,,,,,验证操作员权限,X,权限验证结果,权限状态、错误代码,1
,,,,,,,,更新知识库记录,W,知识库记录,知识库ID、标题、内容、标签、修改时间、版本号,1
,,,,,,,,输出保存结果,X,操作结果,操作状态、错误信息、新版本号,1
,,删除密码知识库数据,"删除密码知识库数据
,平台操作员登录点击密码知识库菜单，点击数据后删除，删除该条记录以及文件
",,,平台操作员在密码知识库页面点击删除按钮，选择数据后删除,删除密码知识库记录,选择密码知识库记录,E,密码知识库记录,记录ID、文件路径、创建时间,1
,,,,,,,,读取待删除记录信息,R,密码知识库记录,记录ID、文件路径、关联业务标识,1
,,,,,,,,删除数据库记录,W,密码知识库记录,记录ID、删除状态标记,1
,,,,,,,删除关联文件,读取文件存储路径,R,文件存储信息,文件路径、存储类型（本地/云）,1
,,,,,,,,执行文件删除操作,W,文件存储信息,文件路径、删除状态,1
,,查询密码知识库数据,查询所有密码知识库记录，可根据文件类型、文档分类、文件名筛选,,发起者：用户，接收者：密码资产数据管理系统-密码文档信息管理模块,用户在密码知识库管理页面点击查询按钮，输入筛选条件,查询密码知识库记录,输入筛选条件,E,筛选条件,文件类型、文档分类、文件名,1
,,,,,,,,读取筛选条件,R,筛选条件,文件类型、文档分类、文件名,1
,,,,,,,,输入分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页信息,R,分页信息,页码、单页数量,1
,,,,,,,,查询密码知识库记录,R,密码知识库记录,文件类型、文档分类、文件名、创建时间、更新时间,1
,,,,,,,,输出查询结果,X,查询结果,记录列表、分页信息,1
,,显示/隐藏知识库信息,点击显示/隐藏可配置是否为租户显示该条记录,,发起者：管理员，接收者：密码资产数据管理系统,管理员点击显示/隐藏知识库信息按钮,查看知识库显示配置状态,查询租户知识库显示配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取租户知识库显示配置,R,知识库显示配置,租户ID、记录ID、显示状态,1
,,,,,,管理员在知识库信息页面点击显示/隐藏按钮，配置租户可见性,更新知识库显示配置,输入租户知识库显示配置,E,知识库显示配置,租户ID、记录ID、显示状态,1
,,,,,,,,保存租户知识库显示配置,W,知识库显示配置,租户ID、记录ID、显示状态,1
,,预览知识库信息,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,,发起者：用户，接收者：密码资产数据管理系统-预览模块,用户点击知识库信息预览按钮,预览知识库信息,输入预览请求,E,预览请求,文件ID、文件类型（文档/表格/PDF/视频/音频）,1
,,,,,,,,读取文件信息,R,文件信息,文件路径、文件格式、文件大小,1
,,,,,,,,处理文件内容,X,处理后的文件数据,文档内容、表格数据、PDF页面、视频帧、音频波形,1
,,,,,,,,生成预览内容,X,预览内容,预览数据、文件格式标识、预览类型（全文/缩略图/时间轴）,1
密码应用测评管理,改造阶段管理,密码应用测评改造阶段分页列表,展示密码应用测评改造过程中划分的不同阶段信息,,发起者：用户，接收者：密码应用测评管理系统-改造阶段管理模块,用户点击密码应用测评改造阶段分页列表菜单,查看密码应用测评改造阶段分页列表信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页配置信息,R,分页配置,默认页码、默认单页数量,1
,,,,,,,,查询密码应用测评改造阶段数据,R,改造阶段数据,阶段名称、开始时间、结束时间、阶段状态、负责人,1
,,,,,,,,输出分页后的改造阶段列表,X,分页改造阶段列表,阶段名称、开始时间、结束时间、阶段状态、负责人,1
,,密码应用测评改造阶段过滤查询,支持根据阶段编码、阶段名称过滤,,发起者：管理员，接收者：密码应用测评管理系统,管理员在改造阶段管理页面点击过滤查询按钮，输入阶段编码/名称进行查询,查看密码应用测评改造阶段过滤结果,输入过滤条件,E,过滤条件,阶段编码、阶段名称,1
,,,,,,,,读取过滤条件,R,过滤条件,阶段编码、阶段名称,1
,,,,,,,,查询改造阶段信息,R,改造阶段信息,阶段编码、阶段名称、阶段描述、创建时间,1
,,,,,,,,输出过滤结果,X,改造阶段信息,阶段编码、阶段名称、阶段描述、创建时间,1
,,,,,,管理员在改造阶段管理页面输入分页参数进行分页查询,分页查看密码应用测评改造阶段过滤结果,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页参数,R,分页信息,页码、单页数量,1
,,,,,,,,查询分页改造阶段信息,R,改造阶段信息,阶段编码、阶段名称、阶段描述、创建时间,1
,,,,,,,,输出分页过滤结果,X,改造阶段信息,阶段编码、阶段名称、阶段描述、创建时间,1
,,新增密码应用测评改造阶段,新增测评改造过程中的阶段信息,4.0,,管理员在改造阶段管理页面点击新增阶段按钮，输入密码应用测评改造阶段信息,新增密码应用测评改造阶段,输入密码应用测评改造阶段信息,E,阶段信息,阶段名称、开始时间、结束时间、阶段描述、负责人,1
,,,,,,,,保存密码应用测评改造阶段信息,W,阶段信息,阶段名称、开始时间、结束时间、阶段描述、负责人,1
,,编辑密码应用测评改造阶段,编辑测评改造过程中的阶段信息,3.0,发起者：管理员，接收者：密码应用测评管理模块,管理员在改造阶段管理页面点击编辑按钮，进入编辑阶段信息界面,查看当前密码应用测评改造阶段信息,查询密码应用测评改造阶段信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用测评改造阶段信息,R,密码应用测评改造阶段信息,阶段名称、阶段状态、开始时间、结束时间、负责人、关联任务,1
,,,,,,管理员在编辑界面提交修改后的密码应用测评改造阶段信息,编辑并保存密码应用测评改造阶段信息,输入修改后的密码应用测评改造阶段信息,E,密码应用测评改造阶段信息,阶段名称、阶段状态、开始时间、结束时间、负责人、关联任务,1
,,,,,,,,验证密码应用测评改造阶段信息,X,校验结果,校验状态、错误信息,1
,,,,,,,,更新密码应用测评改造阶段信息,W,密码应用测评改造阶段信息,阶段名称、阶段状态、开始时间、结束时间、负责人、关联任务,1
,,删除密码应用测评改造阶段,删除测评改造过程中的阶段信息,,发起者：管理员，接收者：密码应用测评管理系统-改造阶段管理模块,管理员在密码应用测评改造阶段管理页面点击删除按钮，选择特定阶段进行删除,删除密码应用测评改造阶段信息,读取待删除的密码应用测评改造阶段信息,R,密码应用测评改造阶段信息,阶段ID、阶段名称、阶段状态、关联测评项目ID,1
,,,,,,,,输入删除操作确认信息,E,删除确认信息,阶段ID、确认操作类型（删除）、操作用户ID、操作时间,1
,,,,,,,,执行密码应用测评改造阶段删除操作,W,密码应用测评改造阶段信息,阶段ID、阶段状态（已删除）,1
,,,,,,,,输出删除操作结果,X,操作结果信息,操作状态（成功/失败）、错误代码、错误描述,1
,,,,,,,,记录密码应用测评改造阶段删除日志,W,系统操作日志,操作类型（删除）、操作对象（阶段ID）、操作用户ID、操作时间、操作IP,1
,,密码应用设置测评改造阶段,设置密码应用当前处于的测评阶段,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评阶段管理页面点击查看当前测评阶段按钮,查看当前密码应用测评阶段,输入查询条件,E,查询条件,无,1
,,,,,,,,读取当前测评阶段信息,R,测评阶段信息,当前阶段名称、阶段描述、生效时间,1
,,,,,,,,输出当前测评阶段信息,X,测评阶段信息,当前阶段名称、阶段描述、生效时间,1
,,,,,,管理员在测评阶段管理页面点击设置测评阶段按钮并提交新阶段,设置密码应用测评阶段,输入新测评阶段信息,E,测评阶段信息,阶段名称、阶段描述、生效时间,1
,,,,,,,,验证测评阶段有效性,R,系统参数,允许的阶段名称列表、时间格式规则,1
,,,,,,,,写入新测评阶段信息,W,测评阶段信息,阶段名称、阶段描述、生效时间,1
,,,,,,,,输出设置结果,X,操作结果,操作状态、错误信息（如有）,1
,,密码应用修改测评改造阶段,修改密码应用当前处于的测评阶段,3.0,发起者：测评管理员，接收者：密码应用测评管理系统,测评管理员在密码应用管理界面点击修改测评阶段按钮,修改密码应用测评阶段,选择目标密码应用,E,密码应用信息,应用ID、应用名称,1
,,,,,,,,输入新测评阶段,E,测评阶段信息,当前阶段、目标阶段,1
,,,,,,,,验证阶段变更规则,R,阶段规则配置,阶段变更规则、依赖条件,1
,,,,,,,,更新密码应用阶段,W,密码应用信息,应用ID、新测评阶段、更新时间,1
,,,,,,,,返回修改结果,X,操作结果,操作状态、错误信息,1
,,,,,发起者：系统，接收者：密码应用测评管理系统,密码应用阶段修改操作触发状态同步,同步关联系统状态,读取关联系统配置,R,系统集成配置,集成系统名称、接口地址,1
,,,,,,,,发送阶段变更通知,X,阶段变更事件,应用ID、新阶段、变更时间,1
,,查询密码应用测评改造阶段,查询密码应用当前处于的测评阶段,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用测评管理页面点击查询改造阶段按钮,查询密码应用当前测评阶段,输入密码应用名称查询条件,E,查询条件,密码应用名称,1
,,,,,,,,读取密码应用测评阶段信息,R,密码应用测评阶段信息,密码应用名称、当前阶段、时间戳,1
,,,,,,,,输出密码应用测评阶段结果,X,密码应用测评阶段信息,密码应用名称、当前阶段、时间戳,1
,,测评改造阶段的应用分布,根据测评改造阶段查询包含的应用数量,5.0,,管理员在改造阶段管理页面点击应用分布查询按钮,查询测评改造阶段的应用数量,输入测评改造阶段信息,E,阶段信息,阶段名称、阶段ID,1
,,,,,,,,读取应用信息,R,应用信息,应用ID、应用名称、所属阶段ID,1
,,,,,,,,统计应用数量,X,统计结果,阶段名称、应用数量,1
,应用测评报告、测评分数管理,应用测评报告分页列表查询,应用测评报告列表展示，展示内容：序号、报告名称、所属应用、报告格式、测评分数、上报时间、操作,3.0,,管理员点击应用测评报告分页列表查询菜单,查看应用测评报告分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页参数,R,分页信息,页码、单页数量,1
,,,,,,,,读取应用测评报告列表数据,R,应用测评报告列表,序号、报告名称、所属应用、报告格式、测评分数、上报时间,1
,,,,,,,,输出分页后的应用测评报告列表,X,应用测评报告列表,序号、报告名称、所属应用、报告格式、测评分数、上报时间、操作,1
,,新增应用测评报告对象,新增测评报告数据对象，录入内容：报告名称、所属应用、报告格式、测评分数,,,管理员在测评报告管理页面点击新增测评报告按钮,新增应用测评报告对象,输入测评报告基本信息,E,测评报告信息,报告名称、所属应用、报告格式、测评分数,1
,,,,,,,,保存测评报告对象,W,测评报告信息,报告名称、所属应用、报告格式、测评分数,1
,,应用测评报告文件上传,上传测评报告文件到文件服务，并绑定到测评报告对象,4.0,发起者：测评人员，接收者：密码应用测评系统-文件服务模块,测评人员在测评报告页面点击文件上传按钮并选择文件,上传测评报告文件,选择并读取本地测评报告文件,E,文件元数据,文件名称、文件大小、文件类型,1
,,,,,,,,将文件上传至文件服务存储系统,W,文件存储对象,文件内容、存储路径、上传时间,1
,,,,,,,,获取文件服务返回的文件唯一标识,R,文件服务响应,文件ID、存储状态,1
,,,,,发起者：测评人员，接收者：密码应用测评系统-测评报告模块,测评人员在测评报告页面点击绑定文件按钮,绑定文件与测评报告,输入当前测评报告的唯一标识,E,测评报告标识,报告ID、报告名称,1
,,,,,,,,关联文件ID与测评报告对象,W,测评报告文件关联,报告ID、文件ID、绑定时间,1
,,,,,,,,返回文件绑定成功状态,X,操作响应,绑定状态、错误信息,1
,,应用测评报告文件预览,页面预览上传的测评报告文件,,发起者：测评人员，接收者：密码应用测评系统,测评人员在测评报告管理页面点击文件预览按钮,查看测评报告文件预览,输入文件预览请求参数,E,文件预览请求,文件ID、预览格式类型,1
,,,,,,,,读取测评报告文件元数据,R,文件元数据,文件ID、文件类型、文件大小,1
,,,,,,,,读取测评报告文件内容,R,文件内容,文件二进制数据、文件格式,1
,,,,,,,,生成预览格式转换指令,W,预览转换任务,目标格式、转换参数,1
,,,,,,,,输出预览页面内容,X,预览页面数据,HTML内容、文件缩略图、分页信息,1
,,,,,,测评人员在文件预览页面进行分页操作,分页查看测评报告文件,输入分页参数,E,分页信息,当前页码、单页显示行数,1
,,,,,,,,读取分页数据范围,R,文件内容分页数据,起始位置、结束位置,1
,,,,,,,,输出分页预览内容,X,分页预览数据,当前页内容、总页数、导航控件,1
,,应用测评报告文件下载,选择测评报告对象，下载上传的测评报告信息,5.0,发起者：测评人员，接收者：密码应用测评管理系统,测评人员在测评报告管理页面点击文件下载按钮,下载测评报告文件,选择测评报告对象,E,报告选择条件,测评报告ID、测评报告名称,1
,,,,,,,,读取测评报告文件信息,R,测评报告存储信息,文件存储路径、文件格式、文件大小,1
,,,,,,,,生成文件下载链接,W,临时下载链接,下载URL、过期时间,1
,,,,,,,,输出测评报告文件,X,下载文件,文件内容、文件类型,1
,,编辑应用测评报告对象,编辑测评报告数据对象，修改名称和描述,3.0,发起者：测评管理员，接收者：密码应用测评系统,测评管理员在测评报告编辑页面点击保存按钮,编辑测评报告对象信息,输入修改后的测评报告名称和描述,E,测评报告对象,报告名称、报告描述,1
,,,,,,,,读取原始测评报告数据,R,测评报告对象,报告ID、原始报告名称、原始报告描述,1
,,,,,,,,更新测评报告数据,W,测评报告对象,报告ID、更新后报告名称、更新后报告描述,1
,,删除应用测评报告对象,删除测评报告数据和对应文件,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评报告列表中点击删除按钮并确认删除操作,删除应用测评报告对象,输入待删除的测评报告ID,E,测评报告标识信息,报告ID,1
,,,,,,,,读取测评报告数据记录,R,测评报告数据,报告ID、报告名称、创建时间、关联文件路径,1
,,,,,,,,删除测评报告数据库记录,W,测评报告数据,报告ID,1
,,,,,,,,删除关联的测评报告文件,W,文件存储系统,文件路径、文件名,1
,,,,,,,,返回删除操作结果,X,操作反馈信息,操作状态（成功/失败）、错误信息,1
,密码应用方案管理,密码应用测评方案分页列表,查看到密码应用测评方案的列表，分页展示,3.0,发起者：用户，接收者：密码应用测评管理模块,用户点击密码应用测评方案分页列表菜单,查看密码应用测评方案分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页参数,R,分页信息,页码、单页数量,1
,,,,,,,,查询密码应用测评方案数据,R,密码应用测评方案信息,方案名称、创建时间、方案状态、关联项目,1
,,,,,,,,输出密码应用测评方案分页列表,X,密码应用测评方案信息,方案名称、创建时间、方案状态、关联项目,1
,,新建密码应用测评方案,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4.0,发起者：管理员，接收者：密码应用测评管理系统,用户在密码应用测评方案界面点击新增按钮,创建密码应用测评方案,选择应用,E,应用信息,应用名称、应用ID,1
,,,,,,,,输入测评基础信息,E,测评基础信息,等保等级、等保状态,1
,,,,,,,,选择密评进度模板,E,密评进度模板,模板名称、模板ID,1
,,,,,,,,选择密评要求模板,E,密评要求模板,模板名称、模板ID,1
,,,,,,,,选择密评交付模板,E,密评交付模板,模板名称、模板ID,1
,,,,,,,,保存测评方案,W,密码应用测评方案,应用ID、等保等级、等保状态、进度模板ID、要求模板ID、交付模板ID,1
,,绑定密码应用测评进度模板,将密码应用测评方案对象绑定测评进度模板,3.0,,管理员在密码应用方案管理页面点击绑定进度模板按钮,绑定密码应用测评方案与进度模板,读取可用的密码应用测评方案列表,R,密码应用测评方案列表,方案名称、方案ID、创建时间,1
,,,,,,,,选择目标密码应用测评方案,E,密码应用测评方案信息,方案ID,1
,,,,,,,,读取可用的测评进度模板列表,R,测评进度模板列表,模板名称、模板ID、版本号,1
,,,,,,,,选择目标测评进度模板,E,测评进度模板信息,模板ID,1
,,,,,,,,执行密码应用测评方案与模板的绑定操作,W,绑定关系数据,方案ID、模板ID、绑定时间,1
,,绑定密码应用测评要求模板,将密码应用测评方案对象绑定测评要求模板,,,管理员在密码应用方案管理页面点击绑定测评要求模板按钮,绑定密码应用测评方案与模板,选择密码应用测评方案,E,测评方案选择信息,测评方案名称、测评方案ID,1
,,,,,,,,读取测评方案详细信息,R,密码应用测评方案,方案名称、方案描述、创建时间,1
,,,,,,,,选择测评要求模板,E,模板选择信息,模板名称、模板版本,1
,,,,,,,,读取模板详细信息,R,密码应用测评要求模板,模板名称、模板内容、适用场景,1
,,,,,,,,确认绑定关系,E,绑定确认信息,绑定时间、操作人,1
,,,,,,,,写入绑定关系,W,测评方案-模板绑定关系,方案ID、模板ID、绑定状态,1
,,,,,,,,返回绑定结果,X,绑定结果信息,绑定状态、错误信息,1
,,密码应用测评进度模板编辑,编辑密码应用测评方案对象绑定的测评进度模板,,发起者：密码测评管理员，接收者：密码应用测评管理系统,管理员在密码应用方案管理页面点击测评进度模板编辑按钮,查看密码应用测评进度模板信息,查询测评进度模板信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取测评进度模板信息,R,密码应用测评进度模板,模板名称、测评阶段、时间安排、责任人、状态,1
,,,,,,管理员在测评进度模板编辑页面修改模板内容并点击保存,编辑密码应用测评进度模板,输入修改后的模板信息,E,密码应用测评进度模板,模板名称、测评阶段、时间安排、责任人、状态,1
,,,,,,,,验证模板数据有效性,X,校验结果,校验状态、错误信息,1
,,,,,,,,更新测评进度模板,W,密码应用测评进度模板,模板名称、测评阶段、时间安排、责任人、状态,1
,,密码应用测评要求模板编辑,编辑密码应用测评方案对象绑定的测评要求模板,,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用方案管理页面点击测评要求模板编辑按钮,查看密码应用测评要求模板信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取测评要求模板信息,R,测评要求模板,模板名称、模板内容、版本号、创建时间,1
,,,,,,管理员在测评要求模板编辑页面点击保存按钮,编辑密码应用测评要求模板,输入模板基本信息,E,模板基本信息,模板名称、版本号、适用场景,1
,,,,,,,,输入模板内容,E,模板内容,测评指标、权重、评分规则,1
,,,,,,,,保存更新后的模板,W,测评要求模板,模板名称、模板内容、版本号,1
,,密码应用测评要求推进,根据测评要求模板条目结合应用现状，勾选测评要求是否已满足（下拉选项，可选满足、不满足）。已满足的条目需要选择一条对应的测评要求调研选项。不满足的页面自动加载出测评要求改进建议。测评要求调研选项和米平要求改进建议支持在页面中二次编辑。,7.0,发起者：测评人员，接收者：密码应用测评管理系统,测评人员点击密码应用测评要求推进菜单,查看测评要求模板条目,查询测评要求模板条目,E,分页信息,页码、单页数量,1
,,,,,,,,读取测评要求模板条目,R,测评要求模板条目,条目编号、条目名称、条目描述,1
,,,,,,测评人员在测评要求条目中勾选满足状态,勾选测评要求是否满足,输入测评要求满足状态,E,测评要求状态,条目编号、满足状态（满足/不满足）,1
,,,,,,,,更新测评要求满足状态,W,测评要求状态,条目编号、满足状态,1
,,,,,,测评人员在已满足条目中选择对应的测评要求调研选项,处理已满足条目,输入测评要求调研选项,E,测评要求调研选项,条目编号、调研选项内容,1
,,,,,,,,更新测评要求调研选项,W,测评要求调研选项,条目编号、调研选项内容,1
,,,,,,测评人员在不满足条目中触发改进建议加载,处理不满足条目,读取测评要求改进建议,R,测评要求改进建议,条目编号、改进建议内容,1
,,,,,,,,输出测评要求改进建议,X,测评要求改进建议,条目编号、改进建议内容,1
,,,,,,测评人员在页面中编辑测评要求调研选项,编辑测评要求调研选项,输入修改后的测评要求调研选项,E,测评要求调研选项,条目编号、修改后的调研选项内容,1
,,,,,,,,更新测评要求调研选项,W,测评要求调研选项,条目编号、修改后的调研选项内容,1
,,,,,,测评人员在页面中编辑测评要求改进建议,编辑测评要求改进建议,输入修改后的测评要求改进建议,E,测评要求改进建议,条目编号、修改后的改进建议内容,1
,,,,,,,,更新测评要求改进建议,W,测评要求改进建议,条目编号、修改后的改进建议内容,1
,,密码应用测评进度推进,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5.0,发起者：测评管理员，接收者：密码应用测评管理系统,测评管理员点击测评进度编辑按钮,查看密码应用测评进度信息,查询测评进度分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取测评要求完成进度数据,R,测评进度信息,测评要求名称、当前完成状态、整改进度关联状态,1
,,,,,,测评管理员在进度编辑页面修改测评要求完成状态并点击保存,编辑密码应用测评进度,输入测评要求完成状态变更信息,E,测评进度修改信息,测评要求名称、目标完成状态、关联整改进度,1
,,,,,,,,校验整改进度关联性,R,整改进度数据,整改项名称、整改完成状态,1
,,,,,,,,更新测评要求完成进度记录,W,测评进度信息,测评要求名称、更新后完成状态、关联整改标识,1
,,密码应用测评进度跟踪报告展示,自动根据测评推进模块和测评进度模板的数据生成流程进度列表。然后可以编辑列表中的预估完成时间、项目当前进度百分比和备注信息。完成后可以保存当日的流程进度列表,4.0,发起者：用户，接收者：密码应用测评管理系统,用户点击生成测评进度跟踪报告,生成测评进度跟踪报告,查询测评进度模板,R,测评进度模板,模板名称、字段配置,1
,,,,,,,,读取测评推进模块数据,R,测评数据,测评项目名称、当前阶段、关联任务,1
,,,,,,,,生成流程进度列表,X,进度列表,项目名称、阶段、任务、预估完成时间、进度百分比、备注,1
,,,,,,用户在进度列表中编辑预估完成时间、进度百分比或备注,编辑测评进度信息,输入预估完成时间,E,进度信息,项目名称、预估完成时间,1
,,,,,,,,输入项目进度百分比,E,进度信息,项目名称、进度百分比,1
,,,,,,,,输入备注信息,E,进度信息,项目名称、备注内容,1
,,,,,,,,更新进度列表数据,W,进度列表,项目名称、预估完成时间、进度百分比、备注,1
,,,,,,用户点击保存当日流程进度列表,保存测评进度列表,校验进度列表数据完整性,R,进度列表,项目名称、预估完成时间、进度百分比,1
,,,,,,,,写入当日进度记录,W,历史进度记录,日期、项目名称、预估完成时间、进度百分比、备注,1
,,密码应用测评进度跟踪报告编辑,针对的是应用测评方案对象，修改密评进度模板详情要素显示或隐藏,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用测评进度模板编辑页面点击要素显示设置按钮,查看密码测评进度模板要素显示状态,查询模板要素显示配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取模板要素显示状态,R,模板详情要素,要素名称、显示状态、更新时间,1
,,,,,,管理员在模板要素显示设置页面修改要素显示/隐藏状态,修改密码测评进度模板要素显示状态,选择模板要素,E,要素信息,要素名称、当前显示状态,1
,,,,,,,,设置显示/隐藏状态,E,状态参数,目标显示状态（显示/隐藏）,1
,,,,,,,,保存模板要素显示配置,W,模板详情要素,要素名称、更新后的显示状态,1
,,密码应用测评进度跟踪报告下载,下载密码应用测评进度的跟踪报告文件,4.0,发起者：测评人员，接收者：密码应用测评管理系统,测评人员在测评进度跟踪页面点击下载报告按钮,生成并下载密码应用测评进度跟踪报告,输入报告生成参数,E,查询参数,测评项目名称、时间范围,1
,,,,,,,,读取测评进度数据,R,测评进度数据,项目名称、测评阶段、完成状态、时间节点,1
,,,,,,,,生成报告文件,W,报告文件,文件内容、文件格式（PDF/Excel）,1
,,,,,,,,输出报告文件,X,报告文件,文件名、文件路径、下载链接,1
,,密码应用测评差距分析内容展示,展示密码应用测评过程中当前的差距分析内容,5.0,,测评人员点击密码应用测评差距分析菜单,展示密码应用测评差距分析内容,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用测评差距分析数据,R,差距分析内容,测评项名称、差距描述、建议措施、风险等级,1
,,,,,,,,输出差距分析结果,X,展示数据,测评项列表、差距详情、建议措施列表,1
,,密码应用测评差距分析内容编辑,编辑密码应用测评过程中当前的差距分析内容,4.0,,测评人员在测评过程中点击差距分析内容编辑按钮,编辑密码应用测评差距分析内容,查询当前测评项目的差距分析内容,E,查询条件,测评项目ID、版本号,1
,,,,,,,,读取当前测评项目的差距分析内容,R,差距分析内容,问题描述、差距项编号、建议措施、风险等级,1
,,,,,,,,输入修改后的差距分析内容,E,差距分析内容,问题描述、差距项编号、建议措施、风险等级,1
,,,,,,,,保存修改后的差距分析内容,W,差距分析内容,问题描述、差距项编号、建议措施、风险等级,1
,,密码应用测评差距分析内容报告生成,生成密码应用测评过程中当前的差距分析报告文件,5.0,发起者：测评管理员，接收者：密码应用测评管理系统,测评管理员点击生成差距分析报告按钮,生成密码应用测评差距分析报告,输入报告生成参数,E,报告参数,测评项目名称、测评时间范围、报告格式（PDF/Word）,1
,,,,,,,,读取当前测评数据,R,测评数据,测评指标名称、当前实现状态、合规等级,1
,,,,,,,,读取密码应用标准要求,R,标准要求,标准编号、要求描述、合规阈值,1
,,,,,,,,执行差距对比分析,X,差距分析结果,差距项描述、风险等级、整改建议,1
,,,,,,,,生成并保存报告文件,W,报告文件,文件路径、文件名称、生成时间戳,1
,,密码应用测评差距分析内容报告导出,导出下载密码应用测评过程中当前的差距分析报告文件,4.0,发起者：用户，接收者：密码应用测评管理模块,用户在测评差距分析页面点击导出报告按钮,生成并导出密码应用测评差距分析报告,输入导出参数,E,导出参数,导出格式（PDF/Excel）、时间范围、测评项目筛选条件,1
,,,,,,,,读取当前差距分析数据,R,测评差距分析数据,测评项名称、差距描述、合规等级、建议措施、责任部门,1
,,,,,,,,生成报告文件,W,生成的报告文件,文件内容（结构化数据）、文件格式、文件校验码,1
,,,,,,,,输出报告文件,X,下载文件,文件名、下载链接、文件大小,1
,密评机构管理,密评机构分页列表查询,查看密评机构信息，查看密评机构的机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱。,3.0,发起者：用户，接收者：密码应用测评管理系统-密评机构管理模块,用户点击密评机构分页列表查询菜单,查看密评机构分页列表信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密评机构分页列表数据,R,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,新增密评机构,在密评机构菜单，点击新增按钮，输入密评机构名称、密评机构地址、密评机构联系人姓名、联系人电话、联系人电子邮箱，点击确认按钮，成功新增密评机构信息。,4.0,发起者：管理员，接收者：密码应用测评管理系统-密评机构管理模块,管理员在密评机构菜单点击新增按钮并输入密评机构信息,新增密评机构信息,输入密评机构信息,E,密评机构信息,密评机构名称、密评机构地址、密评机构联系人姓名、联系人电话、联系人电子邮箱,1
,,,,,,,,保存密评机构信息,W,密评机构信息,密评机构名称、密评机构地址、密评机构联系人姓名、联系人电话、联系人电子邮箱,1
,,编辑密评机构,密评平台可以编辑密评机构信息；在密评机构菜单，点击新增按钮，修改密评机构名称、密评机构地址、密评机构联系人，点击确认按钮，成功编辑密评机构信息。,3.0,发起者：管理员，接收者：密评平台-密评机构管理模块,管理员在密评机构菜单点击新增按钮，修改密评机构信息,编辑密评机构信息,输入密评机构信息,E,密评机构信息,密评机构名称、密评机构地址、密评机构联系人,1
,,,,,,,,读取现有密评机构信息,R,密评机构信息,密评机构ID、密评机构名称、密评机构地址、密评机构联系人,1
,,,,,,,,更新密评机构信息,W,密评机构信息,密评机构ID、密评机构名称、密评机构地址、密评机构联系人,1
,,,,,,,,输出编辑结果,X,操作结果,状态、消息,1
,,删除密评机构,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4.0,发起者：管理员，接收者：密码应用测评管理系统-密评机构管理模块,用户在密评机构信息操作列点击删除按钮并确认删除,删除密评机构信息,读取待删除密评机构信息,R,密评机构信息,密评机构ID、密评机构名称、创建时间,1
,,,,,,,,校验密评机构删除权限,R,用户权限信息,用户ID、操作权限类型,1
,,,,,,,,执行密评机构删除操作,W,密评机构信息,密评机构ID、删除状态,1
,,,,,,,,输出删除操作结果,X,操作反馈信息,操作状态、提示信息,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型分页列表展示,展示密码漏洞/安全事件类型列表信息,3.0,发起者：用户，接收者：密码应用漏洞/安全事件管理模块,用户点击密码漏洞/安全事件类型分页列表菜单,展示密码漏洞/安全事件类型分页列表信息,输入分页参数,E,分页信息,页码、每页数量,1
,,,,,,,,读取分页后的密码漏洞/安全事件类型数据,R,分页后的密码漏洞/安全事件类型数据,类型名称、类型描述、创建时间、更新时间,1
,,,,,,,,输出分页列表信息,X,分页列表,类型名称、类型描述、创建时间、更新时间,1
,,新增密码漏洞/安全事件类型,新增平台监控的密码漏洞/安全事件类型,4.0,发起者：管理员，接收者：密码应用漏洞管理系统,管理员在密码漏洞/安全事件类型管理页面点击新增类型按钮，输入并保存新的密码漏洞/安全事件类型,新增密码漏洞/安全事件类型,输入新类型信息,E,新类型信息,类型名称、类型描述、严重程度等级、检测规则、关联漏洞类别,1
,,,,,,,,校验类型唯一性,R,现有类型信息,类型名称、关联漏洞类别,1
,,,,,,,,保存新类型信息,W,新类型信息,类型名称、类型描述、严重程度等级、检测规则、关联漏洞类别,1
,,编辑密码漏洞/安全事件类型,编辑平台监控的密码漏洞/安全事件类型,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理模块,管理员在密码漏洞/安全事件类型管理页面点击编辑按钮，选择需要修改的密码漏洞/安全事件类型,编辑密码漏洞/安全事件类型信息,读取密码漏洞/安全事件类型信息,R,密码漏洞/安全事件类型信息,类型名称、类型描述、严重等级、监控状态,1
,,,,,,,,输入修改后的密码漏洞/安全事件类型信息,E,密码漏洞/安全事件类型信息,类型名称、类型描述、严重等级、监控状态,1
,,,,,,,,保存修改后的密码漏洞/安全事件类型信息,W,密码漏洞/安全事件类型信息,类型名称、类型描述、严重等级、监控状态,1
,,删除密码漏洞/安全事件类型,删除平台监控的密码漏洞/安全事件类型,,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在密码漏洞/安全事件类型管理页面点击删除按钮,删除密码漏洞/安全事件类型,输入密码漏洞/安全事件类型ID,E,类型标识信息,类型ID,1
,,,,,,,,读取密码漏洞/安全事件类型信息,R,类型基础信息,类型ID、类型名称、类型描述、监控状态,1
,,,,,,,,删除密码漏洞/安全事件类型记录,W,类型基础信息,类型ID、类型名称、类型描述、监控状态,1
,,,,,,,,输出删除操作结果,X,操作结果信息,操作状态（成功/失败）、错误代码、错误描述,1
,,初始化密码漏洞/安全事件类型,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5.0,发起者：系统部署脚本，接收者：密码应用漏洞/安全事件管理系统,系统部署时触发初始化密码漏洞/安全事件类型配置,初始化默认密码漏洞/安全事件类型,读取预定义的密码漏洞/安全事件类型配置文件,R,配置文件,漏洞类型名称、漏洞描述、严重性等级、修复建议,1
,,,,,,,,解析并校验配置文件格式,E,解析后的配置数据,漏洞类型名称、漏洞描述、严重性等级、修复建议,1
,,,,,,,,将校验后的配置数据写入数据库,W,密码漏洞类型表,漏洞类型名称、漏洞描述、严重性等级、修复建议,1
,漏洞/安全事件级别管理,漏洞/安全事件告警分页列表展示,展示平台配置的漏洞/安全事件列表信息,4.0,发起者：系统管理员，接收者：密码应用漏洞/安全事件管理系统,系统管理员点击漏洞/安全事件告警列表菜单,分页展示漏洞/安全事件告警列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取漏洞/安全事件信息,R,漏洞/安全事件信息,漏洞名称、事件级别、发生时间、影响范围、处理状态,1
,,,,,,,,输出分页后的告警列表,X,分页告警列表,漏洞名称、事件级别、发生时间、影响范围、处理状态,1
,,新增漏洞/安全事件告警,新增需要监控的漏洞/安全事件,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件管理页面点击新增告警按钮，输入漏洞/安全事件监控规则,新增漏洞/安全事件告警,输入漏洞/安全事件基本信息,E,漏洞/安全事件告警信息,告警名称、漏洞编号、安全事件类型、监控规则描述、严重级别、触发阈值,1
,,,,,,,,校验输入数据格式和完整性,R,系统校验规则,必填字段规则、字段格式规则、枚举值范围,1
,,,,,,,,保存漏洞/安全事件告警配置,W,漏洞/安全事件告警信息,告警名称、漏洞编号、安全事件类型、监控规则描述、严重级别、触发阈值,1
,,,,,,,,返回新增结果状态,X,操作结果,操作状态（成功/失败）、错误代码、错误描述,1
,,漏洞/安全事件告警启用,启用平台监控的漏洞/安全事件,3.0,,管理员在漏洞/安全事件级别管理页面点击启用告警按钮,启用漏洞/安全事件告警,输入漏洞/安全事件告警启用配置信息,E,告警配置信息,告警级别、启用状态、监控范围、触发阈值,1
,,,,,,,,保存漏洞/安全事件告警配置,W,告警配置信息,告警级别、启用状态、监控范围、触发阈值,1
,,,,,,,,发送告警启用成功通知,X,系统通知信息,通知内容、接收人、时间戳,1
,,漏洞/安全事件告警禁用,禁用平台监控的漏洞/安全事件,,发起者：管理员，接收者：密码应用漏洞/安全事件管理模块,管理员在漏洞/安全事件管理页面点击禁用告警按钮,禁用漏洞/安全事件告警,输入漏洞/安全事件告警标识,E,告警标识信息,告警ID、告警名称,1
,,,,,,,,读取漏洞/安全事件告警详情,R,漏洞/安全事件告警信息,告警ID、告警名称、当前状态,1
,,,,,,,,更新漏洞/安全事件告警状态为禁用,W,漏洞/安全事件告警信息,告警ID、告警名称、状态（禁用）,1
,,,,,,,,输出告警禁用结果,X,操作结果,操作状态、告警ID、禁用时间,1
,,删除告警漏洞/安全事件,删除平台监控的漏洞/安全事件,4.0,,管理员在漏洞/安全事件管理页面点击删除按钮，选择要删除的告警漏洞/安全事件,删除告警漏洞/安全事件,验证用户删除权限,R,用户权限信息,用户ID、权限级别,1
,,,,,,,,输入删除条件,E,删除条件,漏洞ID、事件ID、删除原因,1
,,,,,,,,查询待删除数据,R,漏洞/安全事件信息,漏洞ID、事件ID、漏洞类型、事件级别、关联资产,1
,,,,,,,,执行删除操作,W,漏洞/安全事件记录,漏洞ID、事件ID、删除状态、删除时间,1
,,,,,,,,输出删除结果,X,操作反馈,删除数量、成功/失败标识、错误信息,1
,,漏洞/安全事件告警通知人列表展示,查看监控的漏洞/安全事件触发时通知人信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警通知人列表菜单,展示漏洞/安全事件告警通知人列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取告警通知人列表数据,R,告警通知人信息,通知人姓名、联系方式、所属部门、通知方式、创建时间,1
,,,,,,,,输出告警通知人列表,X,告警通知人列表,通知人姓名、联系方式、所属部门、通知方式、创建时间,1
,,绑定漏洞/安全事件告警通知人,选择监控的漏洞/安全事件触发时通知人,4.0,,管理员点击漏洞/安全事件告警通知人绑定菜单,查看漏洞/安全事件告警通知人绑定信息,查询漏洞/安全事件告警通知人绑定信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取漏洞/安全事件告警通知人绑定信息,R,告警通知人绑定信息,漏洞/安全事件名称、绑定通知人列表,1
,,,,,,管理员在漏洞/安全事件告警通知人绑定页面点击绑定按钮，选择通知人,绑定漏洞/安全事件告警通知人,选择漏洞/安全事件,E,漏洞/安全事件信息,漏洞/安全事件名称、ID,1
,,,,,,,,输入告警通知人信息,E,通知人信息,通知人姓名、联系方式、通知方式,1
,,,,,,,,保存漏洞/安全事件告警通知人绑定,W,告警通知人绑定信息,漏洞/安全事件ID、通知人列表,1
,,新增漏洞/安全事件告警通知人,新增漏洞/安全事件触发时可通知的人员信息和通知方式,,,管理员在漏洞/安全事件配置页面点击新增告警通知人按钮,配置漏洞/安全事件告警通知人信息,输入告警通知人基本信息,E,通知人信息,姓名、联系方式、部门,1
,,,,,,,,选择通知方式,E,通知方式配置,邮件通知、短信通知、企业微信通知,1
,,,,,,,,保存告警通知人配置,W,告警通知人配置,通知人ID、通知方式列表、联系方式,1
,,,,,发起者：系统，接收者：密码应用漏洞/安全事件管理系统,系统检测到漏洞/安全事件触发告警,执行告警通知,读取配置的告警通知人信息,R,告警通知人配置,通知人ID、通知方式列表、联系方式,1
,,,,,,,,发送告警通知,X,告警通知消息,事件类型、事件描述、触发时间、处理建议,1
,,删除漏洞/安全事件告警通知人,删除漏洞/安全事件触发时可通知的人员信息和通知方式,,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件级别管理页面点击删除告警通知人按钮,删除漏洞/安全事件告警通知人信息,输入删除告警通知人请求,E,告警通知人标识,通知人ID、通知人名称,1
,,,,,,,,验证用户删除权限,R,用户权限信息,用户角色、操作权限,1
,,,,,,,,读取待删除的告警通知人信息,R,告警通知人信息,通知人ID、通知人名称、通知方式、联系方式,1
,,,,,,,,删除告警通知人信息,W,告警通知人信息,通知人ID、通知人名称、通知方式、联系方式,1
,,,,,,,,输出删除结果,X,操作结果,删除状态、错误信息,1
,,,,,,管理员在漏洞/安全事件级别管理页面点击查询告警通知人按钮,查询漏洞/安全事件告警通知人信息,输入查询条件,E,查询条件,通知人ID、通知人名称、通知方式,1
,,,,,,,,读取告警通知人信息,R,告警通知人信息,通知人ID、通知人名称、通知方式、联系方式,1
,,,,,,,,输出查询结果,X,告警通知人信息列表,通知人ID、通知人名称、通知方式、联系方式,1
,,告警邮箱服务器配置提交,配置漏洞/安全事件触发时告警信息发送的邮箱配置信息,,发起者：安全管理员，接收者：密码应用漏洞/安全事件管理系统,安全管理员在告警邮箱配置页面点击提交配置按钮,提交邮箱服务器配置信息,输入邮箱服务器配置信息,E,邮箱服务器配置信息,SMTP服务器地址、SMTP服务器端口、邮箱用户名、邮箱密码、加密方式（SSL/TLS）、发件人邮箱地址,1
,,,,,,,,验证邮箱服务器配置格式,R,配置验证规则,IP地址格式、端口范围、邮箱地址格式、加密方式枚举值,1
,,,,,,,,写入邮箱服务器配置,W,邮箱服务器配置信息,SMTP服务器地址、SMTP服务器端口、邮箱用户名、邮箱密码、加密方式、发件人邮箱地址,1
,,,,,,,,输出配置提交结果,X,配置提交反馈,提交成功/失败状态、错误信息（如存在）,1
,,告警邮箱服务器配置查询,查询漏洞/安全事件触发时告警信息发送的邮箱配置信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击告警邮箱服务器配置查询按钮,查询告警邮箱服务器配置信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取邮箱服务器配置数据,R,邮箱服务器配置信息,服务器地址、端口号、用户名、密码、SSL/TLS设置、邮件主题模板,1
,,,,,,,,输出分页后的配置信息,X,查询结果,总记录数、当前页数据列表（含服务器地址、端口、SSL状态等）,1
,,告警邮箱服务器配置重置,清空漏洞/安全事件触发时告警信息发送的邮箱配置信息,,,管理员在告警邮箱配置页面点击重置配置按钮,清空告警邮箱服务器配置信息,输入重置配置指令,E,操作指令,操作类型（重置）、操作时间、操作用户ID,1
,,,,,,,,读取当前邮箱服务器配置,R,邮箱服务器配置信息,服务器地址、服务器端口、认证方式、发送邮箱、接收邮箱列表,1
,,,,,,,,清空邮箱服务器配置数据,W,邮箱服务器配置信息,服务器地址（空字符串）、服务器端口（0）、认证方式（空）、发送邮箱（空）、接收邮箱列表（空数组）,1
,,,,,,,,输出配置重置结果,X,操作结果,操作状态（成功/失败）、错误代码（如有）,1
,,告警验证邮件发送,测试配置的邮箱服务器是否正常，发送测试邮件,5.0,发起者：系统管理员，接收者：密码应用漏洞/安全事件管理系统,系统管理员点击发送测试邮件按钮,发送测试邮件验证邮箱服务器配置,输入发送测试邮件请求,E,测试邮件请求,触发事件、用户ID,1
,,,,,,,,读取邮箱服务器配置信息,R,邮箱服务器配置,SMTP服务器地址、端口、认证信息,1
,,,,,,,,构建测试邮件内容,R,测试邮件模板,邮件主题、正文内容、收件人地址,1
,,,,,,,,发送测试邮件到邮箱服务器,W,邮件传输数据,邮件内容、服务器连接参数,1
,,,,,,,,返回邮件发送结果,X,发送结果反馈,发送状态、错误信息,1
,漏洞/安全事件详情管理,漏洞/安全事件基本信息展示,展示平台的漏洞/安全事件信息配置详细信息,3.0,发起者：管理员/安全人员，接收者：密码应用漏洞/安全事件管理系统,用户点击漏洞/安全事件详情页面的基本信息展示按钮,展示漏洞/安全事件基本信息,输入漏洞/安全事件查询条件,E,查询条件,漏洞ID/安全事件ID,1
,,,,,,,,读取漏洞/安全事件基本信息,R,漏洞/安全事件基本信息,漏洞名称/事件名称、漏洞描述/事件描述、风险等级、发现时间、影响范围、修复建议/处置措施,1
,,,,,,,,输出漏洞/安全事件基本信息至展示界面,X,展示信息,漏洞名称/事件名称、漏洞描述/事件描述、风险等级、发现时间、影响范围、修复建议/处置措施,1
,,漏洞/安全事件信息编辑,编辑平台的漏洞/安全事件信息配置详细信息,,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件编辑按钮,加载漏洞/安全事件基础信息,读取漏洞/安全事件基础信息,R,漏洞/安全事件基础信息,漏洞ID、事件名称、事件类型、发现时间、影响范围,1
,,,,,,管理员在编辑页面输入漏洞/安全事件详细信息,输入漏洞/安全事件详细信息,输入漏洞/安全事件详细信息,E,漏洞/安全事件详细信息,漏洞描述、严重性等级、修复建议、状态、关联资产,1
,,,,,,管理员点击保存按钮,验证并保存漏洞/安全事件信息,验证漏洞/安全事件信息完整性,R,校验规则,必填字段规则、格式校验规则,1
,,,,,,,,写入更新后的漏洞/安全事件信息,W,漏洞/安全事件信息,漏洞ID、事件名称、事件类型、发现时间、影响范围、漏洞描述、严重性等级、修复建议、状态、关联资产,1
,,漏洞/安全事件告警阈值配置,设置漏洞/安全事件的判断监控数据的告警阈值配置,5.0,,用户在告警阈值配置页面点击配置按钮，设置告警阈值,配置漏洞/安全事件告警阈值,输入告警阈值参数,E,告警阈值参数,漏洞类型、阈值数值、时间窗口、触发条件,1
,,,,,,,,验证告警阈值参数有效性,R,系统校验规则,参数格式规则、数值范围限制,1
,,,,,,,,保存告警阈值配置,W,告警阈值配置,漏洞类型、阈值数值、时间窗口、触发条件,1
,,,,,,用户点击告警阈值配置查询按钮,查看漏洞/安全事件告警阈值配置,查询分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取告警阈值配置,R,告警阈值配置,漏洞类型、阈值数值、时间窗口、触发条件,1
,,漏洞/安全事件标签配置,设置漏洞/安全事件监控的告警数据标签,4.0,发起者：安全管理员，接收者：密码应用漏洞/安全事件管理系统,安全管理员点击漏洞/安全事件标签配置菜单,查看漏洞/安全事件标签配置信息,查询漏洞/安全事件标签配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取漏洞/安全事件标签配置,R,漏洞/安全事件标签信息,标签名称、标签描述、创建时间、关联告警类型,1
,,,,,,安全管理员在标签配置页面点击新增/编辑标签按钮，配置漏洞/安全事件标签,配置漏洞/安全事件标签信息,输入漏洞/安全事件标签名称,E,漏洞/安全事件标签信息,标签名称,1
,,,,,,,,输入漏洞/安全事件标签描述,E,漏洞/安全事件标签信息,标签描述,1
,,,,,,,,选择关联告警类型,E,告警类型信息,告警类型名称、告警类型编码,1
,,,,,,,,保存漏洞/安全事件标签配置,W,漏洞/安全事件标签信息,标签名称、标签描述、关联告警类型,1
,,漏洞/安全事件告警组合阈值配置,配置漏洞/安全事件告警阈值的组合判定方式,7.0,,安全管理员点击漏洞/安全事件告警组合阈值配置菜单,查看漏洞/安全事件告警组合阈值配置,查询告警组合阈值配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取告警组合阈值配置,R,告警组合阈值配置,阈值名称、条件组合方式、阈值参数,1
,,,,,,安全管理员在告警组合阈值配置页面点击新增配置按钮,创建漏洞/安全事件告警组合阈值配置,输入告警组合阈值名称,E,阈值基本信息,阈值名称,1
,,,,,,,,选择条件组合方式,E,组合逻辑配置,AND/OR逻辑,1
,,,,,,,,配置阈值参数,E,阈值参数,指标名称、阈值数值、时间窗口,1
,,,,,,,,保存告警组合阈值配置,W,告警组合阈值配置,阈值名称、条件组合方式、阈值参数,1
,,,,,,安全管理员在告警组合阈值配置页面点击编辑按钮,修改漏洞/安全事件告警组合阈值配置,读取待修改的告警组合阈值配置,R,告警组合阈值配置,阈值名称、条件组合方式、阈值参数,1
,,,,,,,,修改告警组合阈值名称,E,阈值基本信息,阈值名称,1
,,,,,,,,修改条件组合方式,E,组合逻辑配置,AND/OR逻辑,1
,,,,,,,,修改阈值参数,E,阈值参数,指标名称、阈值数值、时间窗口,1
,,,,,,,,保存修改后的告警组合阈值配置,W,告警组合阈值配置,阈值名称、条件组合方式、阈值参数,1
,,,,,,安全管理员在告警组合阈值配置页面点击删除按钮,删除漏洞/安全事件告警组合阈值配置,读取待删除的告警组合阈值配置,R,告警组合阈值配置,阈值名称,1
,,,,,,,,确认删除操作,E,操作确认,确认标识,1
,,,,,,,,执行删除告警组合阈值配置,W,告警组合阈值配置,阈值名称,1
,密码产品监控范围管理,密码产品监控列表分布展示,显示平台监控的密码产品对象信息列表,3.0,发起者：管理员，接收者：密服平台-密码产品监控模块,管理员点击密码产品监控列表分布展示菜单,展示密码产品监控对象信息列表,接收分页参数输入,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码产品监控对象数据,R,密码产品监控信息,产品名称、产品型号、监控状态、监控时间、所属区域,1
,,,,,,,,输出分页后的监控列表,X,分页监控列表,总记录数、当前页数据列表（含产品名称、型号、状态等）,1
,,密码产品当前监控数据列表展示,显示监控的密码产品对象的当前监控数据，包含CPU、内存和磁盘信息,4.0,发起者：用户，接收者：密码应用漏洞/安全事件管理系统-密码产品监控范围管理模块,用户访问密码产品监控数据列表页面,查看密码产品当前监控数据列表,查询分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码产品监控数据,R,密码产品监控数据,产品ID、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,,,,,,,输出监控数据列表,X,监控数据列表,产品ID、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,,,,发起者：系统，接收者：密码应用漏洞/安全事件管理系统-密码产品监控范围管理模块,系统定时刷新监控数据,自动刷新密码产品监控数据,读取最新监控数据,R,密码产品监控数据,产品ID、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,,,,,,,更新前端展示数据,X,监控数据列表,产品ID、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,密码产品监控历史数据列表展示,显示监控的密码产品对象的监控历史数据，包含CPU、内存和磁盘信息,5.0,发起者：管理员/监控人员，接收者：密码应用漏洞/安全事件管理系统,用户点击密码产品监控历史数据列表菜单,查看密码产品监控历史数据列表,输入历史数据查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码产品监控历史数据,R,监控历史数据,监控时间、CPU使用率、内存使用率、磁盘使用率、产品标识,1
,,,,,,,,输出历史数据列表,X,历史数据列表,分页数据、CPU/内存/磁盘使用趋势图表,1
,,密码产品当前监控数据折线图,以折线图信息显示监控的密码产品对象的当前监控数据,,发起者：安全管理员，接收者：密码应用漏洞/安全事件管理系统,安全管理员访问密码产品监控折线图页面,展示密码产品实时监控折线图,接收折线图查询请求,E,折线图查询条件,时间范围、产品名称、监控指标类型,1
,,,,,,,,读取实时监控数据,R,密码产品监控数据,时间戳、产品ID、CPU使用率、内存占用、网络流量、漏洞数量,1
,,,,,,,,生成折线图数据结构,X,图表数据,时间序列、指标值数组、产品标识,1
,,,,,,安全管理员在折线图页面调整监控指标配置,配置折线图监控指标,输入监控指标配置,E,监控指标配置,产品ID、指标类型、更新频率、阈值告警规则,1
,,,,,,,,保存监控指标配置,W,监控配置表,产品ID、指标类型、更新频率、阈值告警规则,1
,,密码产品监控历史数据折线图,以折线图信息显示监控的密码产品对象的监控历史数据,,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码产品监控历史数据折线图菜单,查询并展示密码产品监控历史数据折线图,输入查询条件,E,查询参数,时间范围、密码产品名称,1
,,,,,,,,读取监控历史数据,R,监控历史数据,时间戳、监控指标值、产品标识,1
,,,,,,,,生成折线图数据结构,R,折线图数据,时间序列、指标值序列、产品名称,1
,,,,,,,,输出折线图展示数据,X,折线图展示数据,时间轴、数据点坐标、产品标识,1
数据上报接口,密码应用数据上报类接口,密码应用上报数据采集,收集整理需要上报的密码应用数据,4.0,发起者：系统管理员，接收者：数据上报接口模块,系统管理员配置密码应用数据采集任务,采集密码应用数据,输入数据采集参数,E,采集参数,时间范围、数据类型、采集频率,1
,,,,,,,,读取源系统密码应用数据,R,源系统数据,应用名称、使用场景、密钥类型、加密算法、使用频率,1
,,,,,,,,验证数据完整性,X,校验结果,数据完整性状态、缺失字段,1
,,,,,发起者：数据处理服务，接收者：数据上报接口模块,定时任务触发数据整理流程,整理密码应用数据,输入数据整理规则,E,整理规则,字段映射规则、数据格式转换规则,1
,,,,,,,,读取原始采集数据,R,原始采集数据,应用名称、使用场景、密钥类型、加密算法、使用频率,1
,,,,,,,,应用规则转换数据格式,W,整理后数据,标准化字段、统一数据格式,1
,,,,,,,,输出整理结果,X,整理结果,整理状态、异常记录,1
,,密码应用数据上报接口对接,和集团平台对接密码应用数据上报接口,6.0,发起者：系统管理员，接收者：数据上报接口系统,系统管理员在接口配置页面点击密码应用数据上报接口配置按钮,配置密码应用数据上报接口参数,输入接口基础配置信息,E,接口配置信息,接口URL、认证密钥、超时时间、重试次数,1
,,,,,,,,验证接口配置有效性,R,接口配置规则,URL格式校验规则、密钥长度要求,1
,,,,,,,,保存接口配置信息,W,接口配置表,接口名称、URL、认证密钥、创建时间,1
,,,,,,系统管理员在接口测试页面点击连接测试按钮,测试密码应用数据上报接口连接,发送测试请求报文,X,测试请求数据,测试数据包、时间戳、随机字符串,1
,,,,,,,,接收接口响应报文,R,接口响应数据,响应状态码、响应内容、响应时间,1
,,,,,,,,展示连接测试结果,X,测试结果信息,连接状态、错误代码、耗时统计,1
,,密码应用上报数据列表展示,以列表形式显示上报的密码应用数据信息,3.0,发起者：用户，接收者：数据上报接口模块,用户点击密码应用上报数据列表菜单,查看密码应用上报数据列表,查询分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用上报数据,R,密码应用上报数据,应用名称、上报时间、状态、上报内容摘要,1
,,密码应用数据定时上报更新                                        ,根据配置的上报频率，定时上报更新密码应用数据,4.0,发起者：定时任务调度器，接收者：数据上报接口模块,系统根据配置的上报频率触发定时任务,定时上报密码应用数据,读取密码应用数据上报配置,R,上报配置信息,上报频率、下次上报时间、目标地址,1
,,,,,,,,获取当前密码应用数据,R,密码应用数据,应用名称、密钥状态、使用次数、更新时间,1
,,,,,,,,生成上报数据包,E,上报数据包,数据内容、时间戳、校验码,1
,,,,,,,,发送密码应用数据到目标系统,X,网络传输数据,目标地址、数据包内容、传输协议,1
,,,,,,,,记录上报操作日志,W,系统日志,上报时间、目标地址、数据量、状态,1
,,,,,发起者：系统管理员，接收者：数据上报接口模块,管理员修改上报频率配置,更新定时上报配置,输入新的上报频率参数,E,配置参数,分钟间隔、小时间隔、固定时间点,1
,,,,,,,,验证配置参数有效性,R,校验规则,最小间隔、最大间隔、时间格式,1
,,,,,,,,保存更新后的配置,W,上报配置信息,上报频率、下次上报时间,1
,密码资产数据上报类接口,密码产品信息上报数据采集,收集整理需要上报的密码产品信息数据,,,系统管理员启动密码产品信息数据采集任务,采集密码产品信息数据,接收数据采集参数,E,数据采集参数,采集范围、数据源类型、时间范围,1
,,,,,,,,读取密码产品数据源,R,密码产品数据源,数据源类型、连接参数、认证信息,1
,,,,,,,,验证数据完整性,R,数据验证规则,必填字段、格式校验规则、数据范围,1
,,,,,,,,转换数据格式,W,标准化数据格式,字段映射规则、数据清洗规则,1
,,,,,,,,输出采集结果,X,上报数据包,产品名称、型号、生产厂商、技术参数、证书信息,1
,,,,,发起者：定时任务调度器，接收者：数据上报接口模块,系统定时触发密码产品信息数据采集,自动采集密码产品信息数据,读取定时任务配置,R,定时任务配置,执行时间、采集频率、数据源列表,1
,,,,,,,,执行批量数据采集,W,批量采集任务,任务ID、数据源标识、采集状态,1
,,,,,,,,生成采集日志,W,采集日志,任务时间、采集数量、异常记录,1
,,密码产品信息上报接口对接,和集团平台对接密码产品信息数据上报接口,6.0,发起者：系统管理员，接收者：密服平台-数据上报模块,系统管理员点击密码产品信息上报接口配置菜单,查看密码产品信息上报接口配置信息,查询接口配置分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码产品接口配置数据,R,密码产品接口配置,接口名称、接口URL、认证方式、数据格式、状态,1
,,,,,,系统管理员在密码产品接口配置页面点击保存按钮,配置密码产品信息上报接口参数,输入接口基础信息,E,接口基础信息,接口名称、接口URL、认证方式、数据格式,1
,,,,,,,,验证接口参数有效性,E,验证规则,URL格式校验、认证方式校验,1
,,,,,,,,保存接口配置参数,W,密码产品接口配置,接口名称、接口URL、认证方式、数据格式、状态,1
,,,,,,系统管理员在密码产品接口配置页面点击测试连接按钮,测试密码产品信息上报接口连通性,输入测试参数,E,测试参数,测试数据包、认证凭证,1
,,,,,,,,调用密码产品上报接口,X,接口请求,测试数据包、认证凭证,1
,,,,,,,,接收接口响应结果,R,接口响应,响应状态码、响应内容,1
,,,,,,,,输出测试结果,X,测试结果,测试状态、错误信息,1
,,密码产品信息上报数据列表展示,以列表形式显示上报的密码产品信息数据信息,3.0,发起者：用户，接收者：密服平台-数据上报接口模块,用户点击密码产品信息上报数据列表菜单,查看密码产品信息上报数据列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码产品信息上报数据,R,密码产品信息,产品名称、产品型号、上报时间、状态、密钥类型、算法类型,1
,,,,,,,,输出分页后的密码产品信息列表,X,分页密码产品信息列表,总记录数、当前页数据集合（包含产品名称、产品型号、上报时间、状态）,1
,,密码产品信息数据定时上报更新,根据配置的上报频率，定时上报更新密码产品信息数据,4.0,发起者：系统定时任务，接收者：数据上报接口模块,系统根据配置的上报频率触发定时任务,定时上报更新密码产品信息数据,读取密码产品上报配置,R,上报配置信息,上报频率、目标地址、数据格式,1
,,,,,,,,查询待上报的密码产品信息,R,密码产品信息,产品名称、型号、生产厂商、密钥类型、状态,1
,,,,,,,,生成上报数据包,X,数据处理结果,结构化数据格式、时间戳,1
,,,,,,,,发送密码产品信息数据包,X,网络传输数据,目标地址、数据内容、校验码,1
,,,,,,,,记录上报操作日志,W,系统日志,上报时间、数据量、目标地址、状态,1
,,,,,发起者：系统监控模块，接收者：数据上报接口模块,检测到密码产品信息变更事件,触发即时上报更新,接收数据变更通知,E,变更事件信息,产品ID、变更类型、触发时间,1
,,,,,,,,查询变更后的密码产品信息,R,密码产品信息,产品名称、型号、生产厂商、密钥类型、状态,1
,,,,,,,,生成即时上报数据包,X,数据处理结果,结构化数据格式、时间戳,1
,,,,,,,,发送即时上报数据包,X,网络传输数据,目标地址、数据内容、校验码,1
,,,,,,,,记录即时上报日志,W,系统日志,上报时间、数据量、目标地址、状态,1
,,密钥信息上报数据采集,收集整理需要上报的密钥信息信息数据,,发起者：用户，接收者：数据上报接口模块,用户点击密钥信息上报数据采集菜单,收集密钥信息,输入密钥信息,E,密钥信息,密钥名称、密钥值、创建时间、使用状态,1
,,,,,,,,读取密钥信息,R,密钥信息,密钥名称、密钥值、创建时间、使用状态,1
,,,,,发起者：系统，接收者：数据上报接口模块,系统定时触发密钥信息采集任务,整理密钥信息,验证密钥信息格式,E,密钥信息,密钥名称、密钥值、创建时间、使用状态,1
,,,,,,,,分类密钥信息,W,分类后的密钥信息,密钥类型、密钥等级、所属系统,1
,,,,,,,,生成上报数据包,W,上报数据包,密钥信息集合、上报时间、数据校验码,1
,,密钥信息上报接口对接,和集团平台对接密钥信息信息数据上报接口,6.0,发起者：用户/系统管理员，接收者：密服平台-数据上报模块,用户点击密钥信息上报接口配置菜单,查看密钥信息上报接口配置信息,查询密钥上报接口配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取密钥上报接口配置,R,密钥上报接口配置,接口地址、认证信息、数据格式、上报频率,1
,,,,,,用户在密钥信息上报接口配置页面点击保存按钮,配置密钥信息上报接口,输入接口地址信息,E,接口地址信息,接口URL、HTTPS协议版本,1
,,,,,,,,输入认证信息,E,认证信息,API密钥、OAuth令牌、证书路径,1
,,,,,,,,配置数据格式参数,E,数据格式配置,JSON/XML格式、字段映射规则,1
,,,,,,,,设置上报频率,E,定时配置,定时任务周期、最大重试次数,1
,,,,,,,,保存密钥上报接口配置,W,密钥上报接口配置,接口地址、认证信息、数据格式、上报频率,1
,,,,,发起者：系统调度器，接收者：密服平台-数据上报模块,定时任务触发密钥信息上报,执行密钥信息上报,读取待上报密钥数据,R,待上报密钥数据,密钥ID、算法类型、创建时间、使用状态,1
,,,,,,,,转换数据格式,X,数据转换器,JSON/XML转换规则,1
,,,,,,,,发送上报请求,X,HTTP请求,请求头、请求体、认证令牌,1
,,,,,,,,接收上报响应,X,HTTP响应,响应状态码、错误信息,1
,,,,,,,,记录上报日志,W,上报日志,上报时间、接口地址、响应状态、耗时,1
,,密钥信息上报数据列表展示,以列表形式显示上报的密钥信息信息数据信息,3.0,发起者：用户，接收者：数据上报接口模块,用户点击密钥信息上报数据列表菜单,展示密钥信息上报数据列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密钥信息数据,R,密钥信息,密钥名称、密钥类型、创建时间、密钥长度、所属系统,1
,,,,,,,,生成密钥信息列表数据结构,X,密钥信息列表,密钥名称、密钥类型、创建时间、密钥长度、所属系统,1
,,密钥信息数据定时上报更新,根据配置的上报频率，定时上报更新密钥信息信息数据,4.0,发起者：定时任务调度器，接收者：密码资产上报模块,系统根据配置的上报频率触发定时任务,执行密钥信息数据定时上报,读取上报配置信息,R,上报配置信息,上报频率、上次上报时间,1
,,,,,,,,查询待上报的密钥信息,R,密钥信息,密钥ID、密钥类型、密钥状态、创建时间、更新时间,1
,,,,,,,,生成上报数据包,E,上报数据包,密钥ID列表、密钥类型映射、状态变更记录,1
,,,,,,,,调用上报接口发送数据,X,接口响应,上报状态、错误代码、响应时间,1
,,,,,,,,更新上报记录状态,W,上报记录,密钥ID、上报时间、上报结果,1
,,,,,发起者：管理员，接收者：密码资产上报模块,管理员配置或修改上报频率参数,配置密钥信息上报频率,输入上报频率参数,E,频率配置,时间间隔（分钟）、生效时间,1
,,,,,,,,验证频率参数有效性,R,系统规则,最小间隔、最大间隔,1
,,,,,,,,保存频率配置,W,上报配置信息,上报频率、配置时间,1
,,证书信息上报数据采集,收集整理需要上报的证书信息信息数据,,发起者：用户，接收者：数据上报接口-密码资产数据上报模块,用户点击证书信息上报数据采集菜单,查看证书信息上报数据,查询证书信息分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取待上报证书信息,R,证书信息,证书名称、颁发机构、有效期、使用状态,1
,,,,,,用户在证书信息采集页面点击数据采集按钮,采集证书信息数据,输入证书基础信息,E,证书信息,证书名称、颁发机构、有效期、使用状态,1
,,,,,,,,校验证书信息格式,X,校验结果,格式校验状态、错误提示,1
,,,,,,,,保存证书信息数据,W,证书信息,证书名称、颁发机构、有效期、使用状态,1
,,证书信息上报接口对接,和集团平台对接证书信息信息数据上报接口,6.0,发起者：系统管理员，接收者：密服平台-数据上报模块,系统管理员在证书信息上报配置页面点击接口配置按钮,配置证书信息上报接口,输入接口基础信息,E,接口配置信息,接口URL、认证方式、超时时间,1
,,,,,,,,保存接口配置参数,W,接口配置信息,接口URL、认证方式、超时时间,1
,,,,,,,,读取接口配置信息,R,接口配置信息,接口URL、认证方式、超时时间,1
,,,,,发起者：系统，接收者：密服平台-数据上报模块,系统定时任务触发证书信息上报,发送证书信息数据,读取待上报证书数据,R,证书信息,证书名称、颁发机构、有效期、公钥信息,1
,,,,,,,,构建上报数据包,E,上报数据包,证书信息、时间戳、数字签名,1
,,,,,,,,发送证书数据到集团平台,X,上报数据包,证书信息、时间戳、数字签名,1
,,,,,发起者：集团平台，接收者：密服平台-数据上报模块,集团平台返回证书信息上报响应,接收证书信息上报响应,接收集团平台响应数据,X,响应数据,响应状态码、错误信息、接收时间戳,1
,,,,,,,,解析响应数据,E,响应数据,响应状态码、错误信息、接收时间戳,1
,,,,,,,,记录上报结果日志,W,上报日志,证书ID、上报时间、响应状态码、错误信息,1
,,证书信息上报数据列表展示,以列表形式显示上报的证书信息信息数据信息,3.0,发起者：用户，接收者：数据上报接口模块,用户点击证书信息上报数据列表菜单,查询并展示证书信息上报数据列表,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取证书信息上报数据,R,证书信息列表,证书名称、颁发机构、有效期、证书状态、上报时间,1
,,,,,,,,输出证书信息列表数据,X,证书信息列表,证书名称、颁发机构、有效期、证书状态、上报时间,1
,,证书信息数据定时上报更新,根据配置的上报频率，定时上报更新证书信息信息数据,4.0,发起者：定时任务调度器，接收者：数据上报接口模块,系统根据配置的上报频率触发证书信息上报任务,查询待上报的证书信息,读取证书信息配置参数,R,证书上报配置,上报频率、证书类型、过滤条件,1
,,,,,,,,查询待上报证书数据,R,证书信息库,证书名称、有效期、颁发机构、状态、更新时间,1
,,,,,发起者：数据上报接口模块，接收者：外部系统,系统获取到待上报的证书数据,执行证书信息上报,构建证书上报数据包,E,证书上报数据,证书名称、有效期、颁发机构、状态、上报时间戳,1
,,,,,,,,调用外部系统接口上报数据,X,外部系统接口,接口地址、认证信息、数据格式,1
,,,,,发起者：数据上报接口模块，接收者：系统日志服务,证书信息上报操作完成,记录上报操作日志,生成上报操作日志记录,E,操作日志,操作类型、操作时间、操作结果、错误信息,1
,,,,,,,,写入上报操作日志,W,系统日志库,日志ID、操作详情、操作时间,1
,,密码文档信息上报数据采集,收集整理需要上报的密码文档信息信息数据,,发起者：系统管理员，接收者：数据上报接口模块,系统管理员启动密码文档信息采集任务,采集密码文档信息,输入密码文档采集参数,E,采集参数,文档类型、时间范围、部门名称,1
,,,,,,,,读取本地密码文档数据,R,密码文档数据,文档编号、创建时间、加密算法、密级标识,1
,,,,,,,,验证文档数据格式,R,校验规则,字段必填项、数据类型、长度限制,1
,,,,,,,,生成标准化上报数据,W,上报数据集,文档元数据、加密信息、关联业务系统,1
,,,,,,,,写入数据上报队列,W,待上报数据,数据包ID、上报时间、目标接口地址,1
,,,,,发起者：定时任务调度器，接收者：数据上报接口模块,系统定时触发密码文档信息采集,自动采集密码文档信息,读取预设采集规则,R,采集规则,采集频率、文档类型过滤、部门范围,1
,,,,,,,,批量读取密码文档数据,R,批量文档数据,文档编号、创建时间、加密算法、密级标识,1
,,,,,,,,校验批量数据完整性,R,校验规则,字段必填项、数据类型、长度限制,1
,,,,,,,,生成标准化批量上报数据,W,批量上报数据集,文档元数据、加密信息、关联业务系统,1
,,,,,,,,写入批量数据上报队列,W,待上报数据,数据包ID、上报时间、目标接口地址,1
,,密码文档信息上报接口对接,和集团平台对接密码文档信息信息数据上报接口,6.0,发起者：用户，接收者：密服平台-数据上报接口模块,用户点击密码文档信息上报配置菜单,查看密码文档信息上报配置,查询密码文档上报配置信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码文档上报配置,R,密码文档上报配置,集团平台URL、认证令牌、数据格式、上报频率,1
,,,,,,用户在密码文档管理页面点击密码文档上报按钮,上报密码文档信息到集团平台,选择待上报密码文档,E,密码文档列表,文档ID、文档名称、文档类型、创建时间,1
,,,,,,,,读取密码文档内容,R,密码文档信息,文档内容、加密算法、密钥标识、文档状态,1
,,,,,,,,验证接口配置参数,R,接口配置,集团平台URL、认证令牌、数据格式,1
,,,,,,,,构建上报数据包,W,上报数据包,文档元数据、加密内容、时间戳,1
,,,,,,,,发送密码文档到集团平台,X,集团平台接口,请求头、请求体、认证令牌,1
,,,,,,,,接收集团平台响应,R,接口响应,响应状态码、错误信息、处理时间,1
,,,,,,,,更新上报状态记录,W,上报记录,文档ID、上报时间、状态、错误信息,1
,,密码文档信息上报数据列表展示,以列表形式显示上报的密码文档信息信息数据信息,3.0,发起者：用户，接收者：数据上报接口-密码资产数据上报类接口模块,用户点击密码文档信息上报数据列表菜单,展示密码文档信息上报数据列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码文档信息数据,R,密码文档信息,文档ID、文档名称、创建时间、修改时间、状态、上报时间,1
,,,,,,,,生成分页后的列表数据,W,分页列表数据,总记录数、当前页数据列表,1
,,,,,,,,输出列表数据到前端展示,X,前端展示数据,文档名称、创建时间、状态、上报时间,1
,,密码文档信息数据定时上报更新,根据配置的上报频率，定时上报更新密码文档信息信息数据,4.0,发起者：定时任务调度器，接收者：数据上报接口模块,定时任务触发密码文档信息数据上报,定时触发密码文档信息数据上报,读取密码文档上报配置信息,R,密码文档上报配置,上报频率、数据范围、目标地址,1
,,,,,,,,查询待上报的密码文档信息,R,密码文档信息,文档ID、文档内容、更新时间,1
,,,,,,,,生成密码文档上报数据包,X,上报数据包,文档ID列表、内容摘要、时间戳,1
,,,,,,,,发送密码文档信息到目标地址,W,目标系统接口,目标URL、请求头、加密数据体,1
,,,,,,,,记录密码文档上报状态,W,上报日志,文档ID、上报时间、状态码、错误信息,1
,,,,,发起者：管理员，接收者：数据上报接口模块,管理员配置密码文档上报规则,配置密码文档信息上报规则,输入密码文档上报频率,E,定时任务配置,执行周期（分钟/小时/天）,1
,,,,,,,,选择密码文档数据范围,E,数据筛选条件,部门分类、敏感等级、创建时间范围,1
,,,,,,,,设置目标系统接口参数,E,目标系统配置,目标URL、认证令牌、数据格式,1
,,,,,,,,保存密码文档上报配置,W,密码文档上报配置,配置ID、执行周期、数据范围、目标地址,1
,,密码文档文件上传,上传密码文档文件,5.0,发起者：用户，接收者：数据上报接口模块,用户点击密码文档文件上传按钮,上传密码文档文件,选择并提交密码文档文件,E,文件信息,文件名、文件类型、文件大小,1
,,,,,,,,验证文件格式和内容,R,文件元数据,文件扩展名、文件哈希值、文件创建时间,1
,,,,,,,,存储密码文档文件,W,文件存储记录,文件存储路径、文件唯一标识符、上传时间,1
,,,,,,,,返回文件上传结果,X,上传状态,上传状态码、错误信息、文件URL,1
,密码应用测评数据上报类接口,密码应用测评上报数据采集,收集整理需要上报的密码应用测评数据信息数据,4.0,发起者：数据管理员，接收者：数据上报接口模块,用户点击密码应用测评数据采集按钮,收集密码应用测评原始数据,输入数据源配置信息,E,数据源信息,数据源名称、数据源类型、数据库地址、数据库端口、数据库账号、数据库密码,1
,,,,,,,,读取数据源的密码应用测评数据,R,原始测评数据,测评项目编号、测评时间、测评人员、测评结果、备注信息,1
,,,,,,,,验证数据完整性,R,数据校验规则,必填字段列表、字段格式要求、数据范围限制,1
,,,,,,用户在数据采集界面点击数据整理按钮,整理密码应用测评数据,输入数据整理规则,E,整理规则,字段映射关系、数据格式转换规则、数据去重规则,1
,,,,,,,,执行数据格式转换,R,原始测评数据,测评项目编号、测评时间、测评人员、测评结果、备注信息,1
,,,,,,,,生成标准化数据文件,W,标准化数据,统一字段名称、统一数据格式、统一编码规范,1
,,密码应用测评数据上报接口对接,和集团平台对接密码应用测评数据信息数据上报接口,6.0,发起者：系统管理员，接收者：数据上报接口模块,系统管理员点击密码应用测评数据上报按钮,收集并上报密码应用测评数据,读取本地密码应用测评数据,R,密码应用测评原始数据,"测评编号,测评时间,测评结果,测评报告路径",1
,,,,,,,,校验数据格式和完整性,E,数据校验规则,"字段名称,字段类型,必填标识",1
,,,,,,,,生成标准化上报数据包,W,标准化数据包,"加密算法,数据签名,时间戳",1
,,,,,,,,调用集团平台接口进行数据上报,X,接口通信数据,"API地址,请求头,请求体",1
,,,,,,,,接收集团平台返回的上报结果,R,接口响应数据,"状态码,错误信息,交易流水号",1
,,,,,发起者：系统监控模块，接收者：数据上报接口模块,定时任务触发数据上报,自动执行密码应用测评数据上报,查询待上报数据清单,R,待上报数据记录,"数据ID,创建时间,优先级",1
,,,,,,,,批量读取密码应用测评数据,R,密码应用测评原始数据,"测评编号,测评时间,测评结果",1
,,,,,,,,生成批量上报数据包,W,批量数据包,"数据条数,压缩格式,校验码",1
,,,,,,,,异步调用集团平台接口,X,异步通信参数,"回调地址,超时时间,重试策略",1
,,密码应用测评上报数据列表展示,以列表形式显示上报的密码应用测评数据信息数据信息,3.0,发起者：管理员，接收者：数据上报接口模块,管理员点击密码应用测评数据列表展示菜单,查看密码应用测评上报数据列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页查询条件,R,分页信息,页码、单页数量,1
,,,,,,,,查询密码应用测评上报数据,R,密码应用测评数据,测评名称、上报时间、测评状态、上报单位、数据版本,1
,,,,,,,,输出密码应用测评数据列表,X,密码应用测评数据列表,测评名称、上报时间、测评状态、上报单位、数据版本,1
,,密码应用测评数据定时上报更新                                        ,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4.0,发起者：定时任务触发器，接收者：数据上报模块,系统检测到配置的上报时间点,查看定时上报配置信息,查询定时上报配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取定时上报配置详情,R,定时上报配置,上报频率、目标地址、数据格式,1
,,,,,发起者：数据上报模块，接收者：外部系统,定时任务触发数据上报,执行密码应用测评数据定时上报,获取当前时间戳,E,时间信息,当前时间,1
,,,,,,,,查询待上报的测评数据,R,测评数据,测评ID、测评结果、测评时间,1
,,,,,,,,生成上报数据包,W,上报数据,数据内容、加密标识、时间戳,1
,,,,,,,,发送上报数据到目标系统,X,网络传输,目标地址、数据包内容,1
,密码应用漏洞/安全事件上报类接口,密码应用漏洞/安全事件上报数据采集,收集整理需要上报的密码应用漏洞/安全事件信息数据,,发起者：管理员，接收者：数据上报系统,管理员点击密码应用漏洞/安全事件上报数据模板查询按钮,查看密码应用漏洞/安全事件上报数据模板,查询上报数据模板,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报数据模板,R,上报数据模板,字段名称、字段类型、字段描述、必填标识,1
,,,,,,管理员在密码应用漏洞/安全事件上报页面点击数据采集按钮,采集密码应用漏洞/安全事件上报数据,输入漏洞/事件基本信息,E,漏洞/事件信息,事件类型、发生时间、影响范围、严重程度,1
,,,,,,,,输入技术详情,E,技术详情,漏洞名称、漏洞描述、CVSS评分、修复建议,1
,,,,,,,,验证数据完整性,X,校验结果,必填项校验、格式校验、关联性校验,1
,,,,,,,,保存上报数据,W,上报数据记录,事件类型、发生时间、影响范围、漏洞名称、CVSS评分,1
,,,,,,,,生成上报数据报告,X,上报报告,报告标题、数据摘要、完整数据详情,1
,,密码应用漏洞/安全事件上报接口对接,和集团平台对接密码应用漏洞/安全事件信息数据上报接口,6.0,发起者：用户，接收者：密服平台-数据上报接口模块,用户在密码应用漏洞/安全事件上报配置页面点击接口参数配置按钮,配置密码应用漏洞/安全事件上报接口参数,输入接口基础信息,E,接口配置信息,接口URL、认证方式、数据格式,1
,,,,,,,,读取现有接口配置,R,接口配置信息,接口URL、认证方式、数据格式,1
,,,,,,,,保存接口配置参数,W,接口配置信息,接口URL、认证方式、数据格式,1
,,,,,,用户在密码应用漏洞/安全事件上报配置页面点击测试连接按钮,测试密码应用漏洞/安全事件上报接口连通性,发送测试请求,X,测试请求数据,测试数据包、认证令牌,1
,,,,,,,,接收测试响应,R,测试响应数据,响应状态码、错误信息,1
,,,,,发起者：系统，接收者：密服平台-数据上报接口模块,系统检测到密码应用漏洞/安全事件需要上报,执行密码应用漏洞/安全事件数据上报,读取待上报事件数据,R,事件数据,事件类型、发生时间、影响范围、详细描述,1
,,,,,,,,转换数据格式,E,格式转换规则,字段映射规则、数据校验规则,1
,,,,,,,,发送上报数据,X,上报数据包,加密后的事件数据、数字签名,1
,,密码应用漏洞/安全事件上报数据列表展示,以列表形式显示上报的密码应用漏洞/安全事件信息数据信息,3.0,发起者：管理员/安全人员，接收者：数据上报接口模块,用户访问密码应用漏洞/安全事件上报数据列表页面,展示密码应用漏洞/安全事件上报数据列表,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页配置信息,R,分页配置,总记录数、总页数,1
,,,,,,,,读取密码应用漏洞/安全事件上报数据,R,上报数据列表,上报时间、漏洞类型、事件等级、处理状态、上报单位,1
,,,,,,,,输出分页后的数据列表,X,分页数据,当前页数据集合、分页导航信息,1
,,密码应用漏洞/安全事件定时上报更新                                        ,根据配置的上报频率，定时上报更新密码应用漏洞/安全事件信息数据,4.0,发起者：定时任务调度器，接收者：数据上报接口模块,系统根据配置的上报频率触发定时任务,定时上报密码应用漏洞/安全事件更新数据,读取定时上报配置信息,R,定时上报配置,上报频率、数据源、目标地址,1
,,,,,,,,获取待上报的密码应用漏洞/安全事件数据,R,密码应用漏洞/安全事件数据,漏洞ID、事件类型、事件描述、时间戳、状态,1
,,,,,,,,生成上报数据包并发送,X,上报数据包,数据内容、目标地址、请求头,1
,,,,,,,,记录上报结果日志,W,上报日志,上报时间、状态、错误信息,1
,,密码应用漏洞/安全事件补充,补充除平台监控的密码应用漏洞/安全事件的其他安全事件信息,,发起者：安全管理员，接收者：数据上报接口模块,安全管理员在漏洞/事件管理界面点击补充事件按钮,查看可补充的安全事件类型,查询未监控事件类型,E,分页信息,页码、单页数量,1
,,,,,,,,读取事件类型列表,R,事件类型信息,事件类型编号、事件名称、描述,1
,,,,,,安全管理员在补充事件页面填写并提交事件信息,补充安全事件信息,输入事件基本信息,E,事件基础信息,事件类型、发生时间、影响范围,1
,,,,,,,,输入事件详细描述,E,事件详细信息,事件描述、处置建议、附件,1
,,,,,,,,校验事件完整性,X,校验结果,校验状态、错误信息,1
,,,,,,,,保存补充事件记录,W,补充事件数据,事件类型、发生时间、描述、处置建议、附件,1
,,,,,发起者：系统，接收者：数据上报接口模块,系统定时触发未处理事件提醒,生成补充事件提醒,查询待补充事件,R,待处理事件,事件类型、上报时间、处理状态,1
,,,,,,,,生成提醒通知,X,提醒信息,提醒内容、接收人、提醒时间,1
