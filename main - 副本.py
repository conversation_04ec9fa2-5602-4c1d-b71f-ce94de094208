import requests
import re
import json
import pandas as pd
from collections import OrderedDict
from config import ENDPOINT_URL, MODEL_NAME, API_KEY

def call_qwen3(prompt, user_input):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    # 构造消息体
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": user_input}
    ]
    data = {
        "model": MODEL_NAME,
        "messages": messages,
        "temperature": 0.2
    }
    response = requests.post(ENDPOINT_URL, headers=headers, json=data)
    response.raise_for_status()
    # 假设返回格式和openai一致
    content = response.json()["choices"][0]["message"]["content"]
    return extract_json_from_content(content)

def extract_json_from_content(content):
    """
    从大模型返回的 content 字符串中提取并解析 JSON 代码块
    """
    # 用正则表达式提取 ```json ... ``` 之间的内容
    match = re.search(r"```json\s*(.*?)\s*```", content, re.DOTALL)
    if match:
        json_str = match.group(1)
        try:
            # 解析为 Python 对象
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print("JSON解析失败：", e)
            return None
    else:
        print("未找到JSON代码块")
        return None

def flatten_results_with_subprocess(all_items):
    """
    输入：all_items为包含模块信息和大模型返回的所有item（每个item有'子过程'字段，值为列表）
    输出：每个子过程一行的扁平化结果，所有父级信息每行都保留
    """
    flat_results = []
    for item in all_items:
        subprocess_list = item.get('子过程', [])
        # 拷贝除'子过程'外的所有字段
        base_info = {k: v for k, v in item.items() if k != '子过程'}
        for subprocess in subprocess_list:
            row = base_info.copy()
            row.update(subprocess)  # 子过程的key直接变成列
            flat_results.append(row)
    return flat_results

def set_duplicate_modules_to_nan(df, module_cols, right_cols):
    """
    对module_cols做全局去重，对right_cols只在同一个三级模块下做去重。
    """
    # 1. 先对module_cols做全局去重
    for col in module_cols:
        df[col] = df[col].where(df[col] != df[col].shift(), None)
    # 2. 对right_cols在同一个三级模块下做去重
    level_3_col = module_cols[-1]  # 假设最后一个是三级模块
    for col in right_cols:
        if col in df.columns:
            df[col] = df.groupby(level_3_col, group_keys=False)[col].apply(lambda x: x.where(x != x.shift(), None))
    return df


if __name__ == "__main__":
    excel_file = "附件4：中国移动河南公司2025年移动爱家终端管理平台研发项目-软件功能清单.xlsx"
    level_1_name, level_2_name, level_3_name = "一级功能模块", "二级功能模块","三级功能模块"
    function_description_name = "功能描述"
    estimated_workload_name = "预估工作量\n（人天）"
    sheet_name = 1
    header = 0

    with open('prompt.md', 'r', encoding='utf-8') as f:
        prompt = f.read()
    
    df = pd.read_excel(excel_file, sheet_name = sheet_name, header = header)

    
    df[[level_1_name, level_2_name]] = df[[level_1_name, level_2_name]].ffill()

    all_results = []
    user_inputs = []
    current_level_2 = None
    current_level_1 = None
    current_level_3_list = []
    current_function_description_list = []
    current_estimated_workload_list = []
    current_function_description = None
    current_estimated_workload = None
    count = 0  # 计数器
    
    for idx, row in df.iterrows():
        level_1_module = str(row[level_1_name])
        level_2_module = str(row[level_2_name])
        level_3_module = str(row[level_3_name])
        function_description = str(row[function_description_name])
        estimated_workload = str(row[estimated_workload_name])
        # 跳过无效三级模块
        if pd.isna(level_3_module) or str(level_3_module).strip().lower() in ['nan', 'none', '']:
            continue

        # 如果遇到新的二级功能模块，先处理上一个
        if (current_level_2 is not None) and (level_2_module != current_level_2 or level_1_module != current_level_1):
            print(current_level_3_list)
            print(current_function_description_list)
            print(current_estimated_workload_list)
            # 累计user_input字符串
            batch_user_input = "\n".join(user_inputs)
            print(f"【{current_level_1}-{current_level_2}】累计user_input：\n{batch_user_input}")
            result = call_qwen3(prompt, batch_user_input)
            print(json.dumps(result, ensure_ascii=False, indent=4))
            print("="*50)
            # 结果处理
            if result and isinstance(result, list):
                for item, level_3, func_desc, est_work in zip(result, current_level_3_list, current_function_description_list, current_estimated_workload_list):
                    ordered_item = OrderedDict()
                    ordered_item[level_1_name] = current_level_1
                    ordered_item[level_2_name] = current_level_2
                    ordered_item[level_3_name] = level_3
                    ordered_item[function_description_name] = func_desc
                    ordered_item[estimated_workload_name] = est_work
                    for k, v in item.items():
                        ordered_item[k] = v 
                    all_results.append(ordered_item)
            
            # 清空累计
            user_inputs = []
            current_level_3_list = []
            current_function_description_list = []
            current_estimated_workload_list = []
            break
            
        
        # 累计当前三级功能模块的user_input
        user_input = f"{level_1_name}：{level_1_module}，{level_2_name}：{level_2_module}，{level_3_name}：{level_3_module}, {function_description_name}: {function_description}, {estimated_workload_name}: {estimated_workload}"
        user_inputs.append(user_input)

        current_level_3_list.append(level_3_module)
        current_function_description_list.append(function_description)
        current_estimated_workload_list.append(estimated_workload)

        current_level_3 = level_3_module
        current_level_2 = level_2_module
        current_level_1 = level_1_module
        current_function_description = function_description
        current_estimated_workload = estimated_workload
    
    # 循环结束后，处理最后一组
    """
    if user_inputs:
        batch_user_input = "\n".join(user_inputs)
        print(f"【{current_level_1}-{current_level_2}】累计user_input：\n{batch_user_input}")
        result = call_qwen3(prompt, batch_user_input)
        print(json.dumps(result, ensure_ascii=False, indent=4))
        print("="*50)
        if result and isinstance(result, list):
            for item in result:
                ordered_item = OrderedDict()
                ordered_item[level_1_name] = current_level_1
                ordered_item[level_2_name] = current_level_2
                ordered_item[function_description_name] = current_function_description
                ordered_item[estimated_workload_name] = current_estimated_workload
                for k, v in item.items():
                    ordered_item[k] = v
    """
   
    
    flat_results = flatten_results_with_subprocess(all_results)
    result_df = pd.DataFrame(flat_results)
    module_cols = [level_1_name, level_2_name, level_3_name]
    right_cols = [function_description_name, estimated_workload_name, '功能用户', '触发事件', '功能过程']
    result_df = set_duplicate_modules_to_nan(result_df, module_cols, right_cols)
    result_df.to_csv('output.csv', index=False, encoding='utf-8-sig')