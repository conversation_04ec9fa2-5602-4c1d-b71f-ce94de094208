一级功能模块,二级功能模块,三级功能模块,功能描述,"预估工作量
（人天）",功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
系统管理,总部一级平台对接,总部平台上报路径配置,支持配置总部平台的上报路径，上报路径为集团平台的调用路径。支持ipv4及ipv6，支持对地址格式进行检测校验,4.0,发起者：管理员，接收者：系统管理模块-总部一级平台对接,管理员在上报路径配置页面点击保存按钮,配置总部平台上报路径,输入上报路径信息,E,上报路径信息,上报URL、IP地址、端口号、协议类型,1
,,,,,,,,校验IP地址格式,E,地址校验规则,IPv4/IPv6格式规范,1
,,,,,,,,保存上报路径配置,W,上报路径配置表,上报URL、IP地址、端口号、协议类型,1
,,总部平台上报路径查看,展示总部平台上报路径,2.0,发起者：管理员，接收者：系统管理模块-总部一级平台对接,管理员点击上报路径查看菜单,查看总部平台上报路径,查询上报路径配置,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报路径配置,R,上报路径配置表,上报URL、IP地址、端口号、协议类型,1
,,总部平台HTTPS通道对接,支持通过HTTPS通道与总部平台对接，满足总部平台对TLS协议及加密套件要求,3.0,发起者：管理员，接收者：系统管理模块-总部一级平台对接,管理员在HTTPS通道配置页面点击保存按钮,配置HTTPS通道参数,输入HTTPS参数,E,HTTPS参数,TLS版本、加密套件列表、证书有效期,1
,,,,,,,,验证证书有效性,E,证书校验规则,有效期、颁发机构、域名匹配,1
,,,,,,,,保存HTTPS配置,W,HTTPS通道配置表,TLS版本、加密套件列表,1
,,总部平台访问凭证配置,支持配置调用集团平台使用的AKSK信息,3.0,发起者：管理员，接收者：系统管理模块-总部一级平台对接,管理员在访问凭证配置页面点击保存按钮,配置访问凭证信息,输入AKSK信息,E,访问凭证信息,AccessKeyID、SecretAccessKey,1
,,,,,,,,加密存储凭证,W,加密凭证存储表,加密后的AccessKeyID、SecretAccessKey,1
,,访问凭证查看,展示总部平台访问AK,2.0,发起者：管理员，接收者：系统管理模块-总部一级平台对接,管理员点击访问凭证查看菜单,查看访问凭证信息,查询访问凭证,E,分页信息,页码、单页数量,1
,,,,,,,,读取AccessKeyID,R,访问凭证信息,AccessKeyID,1
,,AKSK认证,通过配置的AKSK，使用密码算法保护后与总部平台进行对接认证,5.0,发起者：系统，接收者：总部平台接口服务,系统定时发起认证请求,执行AKSK认证,读取加密凭证,R,加密凭证存储表,加密AccessKeyID、SecretAccessKey,1
,,,,,,,,解密凭证信息,E,解密算法参数,解密密钥、算法类型,1
,,,,,,,,生成认证请求报文,E,认证请求参数,时间戳、签名算法、签名值,1
,,,,,,,,发送认证请求,X,HTTPS请求报文,认证请求体、TLS加密数据,1
,用户认证管理,用户列表查询,列表展示内容：序号、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,2.0,发起者：管理员，接收者：用户认证管理系统,管理员点击用户列表查询菜单,查看用户列表信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户列表数据,R,用户列表信息,序号、账号名、姓名、角色、所属租户、账号有效期、完整性、备注、创建时间、状态,1
,,用户注册,注册用户信息，录入内容：账户名、姓名、用户类型、备注,3.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户注册页面点击保存按钮,注册用户信息,输入用户注册信息,E,用户注册信息,账户名、姓名、用户类型、备注,1
,,,,,,,,保存用户注册信息,W,用户注册信息,账户名、姓名、用户类型、备注,1
,,用户编辑,编辑用户信息，编辑用户名称,4.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户编辑页面点击保存按钮,编辑用户信息,输入用户编辑信息,E,用户编辑信息,用户名称,1
,,,,,,,,保存用户编辑信息,W,用户编辑信息,用户名称,1
,,启用,启用禁用的用户,2.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击启用按钮,启用用户,选择启用用户,E,用户状态信息,用户ID,1
,,,,,,,,更新用户状态为启用,W,用户状态信息,用户ID、状态,1
,,禁用,禁用用户，禁止用户登录,2.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击禁用按钮,禁用用户,选择禁用用户,E,用户状态信息,用户ID,1
,,,,,,,,更新用户状态为禁用,W,用户状态信息,用户ID、状态,1
,,重置密码,重置用户登录口令为默认口令,2.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击重置密码按钮,重置用户密码,选择重置密码用户,E,密码信息,用户ID,1
,,,,,,,,保存默认密码,W,密码信息,用户ID、默认密码,1
,,解锁,解锁到期用户或多次录入错误口令的用户,2.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击解锁按钮,解锁用户,选择解锁用户,E,解锁信息,用户ID,1
,,,,,,,,更新用户解锁状态,W,解锁信息,用户ID、解锁原因,1
,,设置有效期,设置用户的有效期,3.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击设置有效期按钮,设置用户有效期,输入用户有效期信息,E,有效期信息,用户ID、有效期,1
,,,,,,,,保存用户有效期,W,有效期信息,用户ID、有效期,1
,,删除,删除用户,2.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户管理页面点击删除按钮,删除用户,选择删除用户,E,用户信息,用户ID,1
,,,,,,,,删除用户记录,W,用户信息,用户ID,1
,,列表查询,列表展示内容：序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,3.0,发起者：管理员，接收者：用户认证管理系统,管理员点击用户注册审核列表查询菜单,查看用户注册审核列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户注册审核列表数据,R,用户注册审核信息,序号、账号名、姓名、申请人、申请时间、备注、审核状态、审核时间、审核人、审核意见,1
,,删除注册记录,删除注册用户的记录信息,2.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户注册审核页面点击删除按钮,删除注册记录,选择删除注册记录,E,注册记录信息,记录ID,1
,,,,,,,,删除注册记录,W,注册记录信息,记录ID,1
,,用户注册审核,审核通过/拒绝、所属角色、审批意见,3.0,发起者：管理员，接收者：用户认证管理系统,管理员在用户注册审核页面点击审核按钮,审核用户注册,输入审核意见和角色,E,审核信息,审核状态、所属角色、审批意见,1
,,,,,,,,保存审核结果,W,审核信息,审核状态、所属角色、审批意见,1
,访问控制管理,口令登录,使用用户口令进行登录,3.0,发起者：用户，接收者：系统管理模块,用户点击登录按钮并输入口令,用户口令登录验证,输入用户名和口令,E,用户登录信息,用户名、口令,1
,,,,,,,,验证用户名和口令有效性,R,用户账户信息,用户名、加密口令、账户状态,1
,,,,,,,,生成登录响应结果,X,登录结果,登录状态、错误信息,1
,,Ukey登录,使用Ukey智能密钥进行用户登录,5.0,发起者：用户，接收者：系统管理模块,用户插入UKey并输入密钥口令,UKey智能密钥登录验证,读取UKey设备信息,R,UKey设备信息,序列号、公钥证书,1
,,,,,,,,输入UKey口令,E,UKey口令,口令文本,1
,,,,,,,,验证UKey口令与设备绑定关系,R,UKey绑定信息,用户ID、设备序列号,1
,,,,,,,,生成登录响应结果,X,登录结果,登录状态、错误信息,1
,,口令黑名单列表查询,查询禁用的登录口令，禁止用户使用安全级别低的口令进行登录,2.0,发起者：管理员，接收者：系统管理模块,管理员点击口令黑名单查询按钮,查询禁用口令列表,输入查询条件（分页参数）,E,分页信息,页码、单页数量,1
,,,,,,,,读取禁用口令列表数据,R,禁用口令列表,口令内容、创建时间、禁用原因,1
,,,,,,,,展示禁用口令列表,X,查询结果,口令列表、分页信息,1
,,新建口令黑名单,新增用户登录口令黑名单,3.0,发起者：管理员，接收者：系统管理模块,管理员点击新增口令黑名单按钮,新增禁用口令,输入禁用口令内容,E,禁用口令,口令文本、禁用原因,1
,,,,,,,,保存禁用口令记录,W,禁用口令列表,口令内容、创建时间、禁用原因,1
,,编辑口令黑名单,编辑用户登录口令黑名单,2.0,发起者：管理员，接收者：系统管理模块,管理员点击编辑口令黑名单按钮,编辑禁用口令,选择待编辑的禁用口令,E,选择项,口令ID,1
,,,,,,,,输入更新后的禁用口令内容,E,禁用口令,口令文本、禁用原因,1
,,,,,,,,更新禁用口令记录,W,禁用口令列表,口令内容、更新时间、禁用原因,1
,,删除口令黑名单,删除用户登录口令黑名单,2.0,发起者：管理员，接收者：系统管理模块,管理员点击删除口令黑名单按钮,删除禁用口令,选择待删除的禁用口令,E,选择项,口令ID,1
,,,,,,,,执行删除操作,W,禁用口令列表,删除标记,1
,,智能密码钥匙列表,列表展示内容：序列号、类型、所属账号名、所属用户名,4.0,发起者：管理员，接收者：系统管理模块,管理员点击智能密码钥匙列表按钮,查询UKey设备列表,输入查询条件（分页参数）,E,分页信息,页码、单页数量,1
,,,,,,,,读取UKey设备信息,R,UKey设备列表,序列号、类型、所属账号名、所属用户名、状态,1
,,,,,,,,展示UKey设备列表,X,查询结果,设备列表、分页信息,1
,,智能密码钥匙新增,选择智能密钥钥匙类型，录入Ukey口令，选择绑定的用户信息,4.0,发起者：管理员，接收者：系统管理模块,管理员点击新增智能密码钥匙按钮,新增UKey设备,选择UKey设备类型,E,设备类型,设备型号,1
,,,,,,,,输入UKey口令,E,UKey口令,口令文本,1
,,,,,,,,选择绑定用户,E,用户信息,用户ID、用户名,1
,,,,,,,,保存UKey设备绑定信息,W,UKey绑定信息,序列号、用户ID、设备类型、绑定时间,1
,,智能密码钥匙启用,启用禁用的UKey智能密码钥匙,4.0,发起者：管理员，接收者：系统管理模块,管理员点击启用UKey按钮,启用UKey设备,选择待启用的UKey设备,E,选择项,设备序列号,1
,,,,,,,,更新设备状态为启用,W,UKey设备状态,序列号、状态,1
,,智能密码钥匙禁用,禁用Ukey智能密码钥匙，禁止使用该Ukey进行登录,4.0,发起者：管理员，接收者：系统管理模块,管理员点击禁用UKey按钮,禁用UKey设备,选择待禁用的UKey设备,E,选择项,设备序列号,1
,,,,,,,,更新设备状态为禁用,W,UKey设备状态,序列号、状态,1
,,智能密码钥匙删除,删除Ukey智能密码钥匙凭证,4.0,发起者：管理员，接收者：系统管理模块,管理员点击删除UKey按钮,删除UKey设备,选择待删除的UKey设备,E,选择项,设备序列号,1
,,,,,,,,执行UKey设备删除操作,W,UKey设备记录,删除标记,1
,,是否开启口令登录,是否开启口令登录，允许用户使用口令进行登录,4.0,发起者：管理员，接收者：系统管理模块,管理员切换口令登录开关状态,配置口令登录启用状态,读取当前口令登录配置,R,登录配置,口令登录启用状态,1
,,,,,,,,更新口令登录启用状态,W,登录配置,启用状态,1
,,是否开启UKey登录,是否开启UKey登录，允许用户使用Ukey进行登录,2.0,发起者：管理员，接收者：系统管理模块,管理员切换UKey登录开关状态,配置UKey登录启用状态,读取当前UKey登录配置,R,登录配置,UKey登录启用状态,1
,,,,,,,,更新UKey登录启用状态,W,登录配置,启用状态,1
,,默认口令,默认口令，设置用户注册后默认口令信息,2.0,发起者：管理员，接收者：系统管理模块,管理员设置默认口令,配置默认口令,输入默认口令内容,E,默认口令,口令文本,1
,,,,,,,,保存默认口令配置,W,系统配置,默认口令,1
,,历史口令限制次数,历史口令限制次数，限制修改口令时，和向上几次历史口令不同,2.0,发起者：管理员，接收者：系统管理模块,管理员设置历史口令限制次数,配置历史口令限制,输入历史口令限制次数,E,限制参数,限制次数,1
,,,,,,,,保存历史口令限制配置,W,系统配置,历史口令限制次数,1
,,长时间未登录禁用账户天数,长时间未登录禁用账户天数，设置用户多长时间不登录后，自动锁定用户,2.0,发起者：管理员，接收者：系统管理模块,管理员设置长时间未登录禁用天数,配置账户自动锁定规则,输入禁用天数,E,限制参数,天数,1
,,,,,,,,保存自动锁定配置,W,系统配置,禁用天数,1
,,口令有效期天数,设置用户口令有效期天数,2.0,发起者：管理员，接收者：系统管理模块,管理员设置口令有效期天数,配置口令有效期,输入口令有效期天数,E,限制参数,天数,1
,,,,,,,,保存口令有效期配置,W,系统配置,有效期天数,1
,,口令有效期告警天数,口令有效期告警天数，口令即将到期告警天数,2.0,发起者：管理员，接收者：系统管理模块,管理员设置口令有效期告警天数,配置口令到期告警,输入告警天数,E,限制参数,天数,1
,,,,,,,,保存告警配置,W,系统配置,告警天数,1
,,登录失败次数限制次数,登录失败次数限制次数，登录口令允许错误次数，超过锁定对应用户,2.0,发起者：管理员，接收者：系统管理模块,管理员设置登录失败次数限制,配置登录失败锁定规则,输入允许失败次数,E,限制参数,次数,1
,,,,,,,,保存失败次数限制配置,W,系统配置,失败次数限制,1
,,登录失败锁定时长(分钟),登录失败锁定时长(分钟)，,2.0,发起者：管理员，接收者：系统管理模块,管理员设置登录失败锁定时长,配置登录失败锁定时长,输入锁定时长（分钟）,E,限制参数,分钟数,1
,,,,,,,,保存锁定时长配置,W,系统配置,锁定时长,1
,,是否强制修改默认口令,是否强制修改默认口令,2.0,发起者：管理员，接收者：系统管理模块,管理员切换强制修改默认口令开关,配置强制修改默认口令规则,读取当前强制修改配置,R,系统配置,强制修改标志,1
,,,,,,,,更新强制修改配置,W,系统配置,强制修改标志,1
,上报周期管理,上报内容列表,列表展示上报内容、上报周期、是否启用、上报周期、最后上报时间,4.0,发起者：管理员，接收者：系统管理模块,管理员访问上报内容列表页面,查看上报内容列表信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取上报内容列表数据,R,上报内容信息,上报内容名称、上报周期、是否启用、最后上报时间,1
,,,,,,,,输出上报内容列表数据,X,上报内容信息,上报内容名称、上报周期、是否启用、最后上报时间,1
,,上报内容配置,"提供上报内容配置选项,可以控制对应上报项目是否开启上报",4.0,发起者：管理员，接收者：系统管理模块,管理员在上报内容配置页面点击保存按钮,配置上报内容启用状态,输入上报内容配置信息,E,上报内容配置,上报内容ID、是否启用,1
,,,,,,,,验证上报内容配置信息,R,上报内容配置规则,配置项校验规则,1
,,,,,,,,保存上报内容配置信息,W,上报内容配置,上报内容ID、是否启用,1
,,上报频率配置,"提供上报内容上报频率配置选项,可以控制对应上报项目的上报频率",7.0,发起者：管理员，接收者：系统管理模块,管理员在上报频率配置页面点击保存按钮,配置上报频率参数,输入上报频率配置信息,E,上报频率配置,上报内容ID、上报频率类型、具体频率值,1
,,,,,,,,验证上报频率配置信息,R,频率配置规则,频率类型校验规则,1
,,,,,,,,保存上报频率配置信息,W,上报频率配置,上报内容ID、上报频率类型、具体频率值,1
,日志管理/统计分析,登录日志查询,记录平台所有登录日志，包含成功和异常,4.0,发起者：管理员，接收者：系统管理模块-日志管理,管理员点击登录日志查询菜单,查看登录日志信息,输入查询条件,E,查询条件,时间范围、用户ID、登录状态,1
,,,,,,,,读取登录日志记录,R,登录日志数据,登录时间、用户ID、IP地址、登录状态,1
,,,,,,,,展示查询结果,X,日志展示数据,分页信息、日志列表,1
,,批量审计,批量审批登录日志,4.0,发起者：管理员，接收者：系统管理模块-日志管理,管理员在登录日志列表中选择多条记录并点击批量审批,批量审批登录日志,选择待审批日志,E,日志选择项,日志ID列表,1
,,,,,,,,输入审批意见,E,审批信息,审批状态、审批人、备注,1
,,,,,,,,更新日志审批状态,W,登录日志数据,审批状态、审批时间,1
,,日志导出,导出登录日志,4.0,发起者：管理员，接收者：系统管理模块-日志管理,管理员在登录日志页面点击导出按钮,导出登录日志,选择导出格式,E,导出配置,文件格式（CSV/Excel）,1
,,,,,,,,生成导出文件,R,登录日志数据,导出字段（时间、用户ID、IP地址等）,1
,,,,,,,,下载导出文件,X,导出文件,文件名称、下载链接,1
,,操作日志查询,记录平台和租户的所有操作日志，包含各类密码服务的配置,4.0,发起者：管理员，接收者：系统管理模块-日志管理,管理员点击操作日志查询菜单,查看操作日志信息,输入查询条件,E,查询条件,时间范围、操作类型、用户ID,1
,,,,,,,,读取操作日志记录,R,操作日志数据,操作时间、用户ID、操作类型、配置详情,1
,,,,,,,,展示查询结果,X,日志展示数据,分页信息、日志列表,1
,,批量审批,批量审批操作日志,4.0,发起者：管理员，接收者：系统管理模块-日志管理,管理员在操作日志列表中选择多条记录并点击批量审批,批量审批操作日志,选择待审批日志,E,日志选择项,日志ID列表,1
,,,,,,,,输入审批意见,E,审批信息,审批状态、审批人、备注,1
,,,,,,,,更新日志审批状态,W,操作日志数据,审批状态、审批时间,1
,,日志导出,导出操作日志,,,管理员在操作日志页面点击导出按钮,导出操作日志,选择导出格式,E,导出配置,文件格式（CSV/Excel）,1
,,,,,,,,生成导出文件,R,操作日志数据,导出字段（时间、用户ID、操作类型等）,1
,,,,,,,,下载导出文件,X,导出文件,文件名称、下载链接,1
密码应用数据管理,密码应用类型管理,密码应用类型分页列表查询,分页展示平台支持的密码应用类型，列表内容：序号、类型编码、类型名称、备注、创建时间、操作,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用类型分页列表查询菜单,查看密码应用类型分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用类型列表数据,R,密码应用类型列表,类型编码、类型名称、备注、创建时间,1
,,密码应用类型过滤查询,支持根据类型名称和编码进行过滤查询,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用类型列表页面输入类型名称或编码进行过滤查询,查看密码应用类型过滤列表,输入过滤条件,E,过滤条件,类型名称、类型编码,1
,,,,,,,,读取过滤后的密码应用类型列表,R,密码应用类型列表,类型编码、类型名称、备注、创建时间,1
,,新增密码应用类型,新增密码应用类型，录入类型编码、类型名称、备注,4.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新增密码应用类型按钮并提交表单,新增密码应用类型,输入新增密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1
,,,,,,,,写入新增密码应用类型数据,W,密码应用类型,类型编码、类型名称、备注,1
,,编辑密码应用类型,编辑密码应用类型，编辑类型编码、类型名称、备注,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用类型列表中点击编辑按钮并提交修改,编辑密码应用类型,读取待编辑密码应用类型信息,R,密码应用类型信息,类型编码、类型名称、备注,1
,,,,,,,,输入修改后的密码应用类型信息,E,密码应用类型信息,类型编码、类型名称、备注,1
,,,,,,,,写入修改后的密码应用类型数据,W,密码应用类型,类型编码、类型名称、备注,1
,,删除密码应用类型,应用类型下无对应用时，允许删除密码应用类型,4.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用类型列表中点击删除按钮,删除密码应用类型,写入删除密码应用类型数据,W,密码应用类型,类型编码,1
,,密码应用类型下拉选择,创建应用或过滤查询时，获取类型名称和ID,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在创建应用或过滤查询页面选择密码应用类型,获取密码应用类型下拉选项,读取密码应用类型下拉选项数据,R,密码应用类型下拉选项,类型名称、类型ID,1
,,密码应用类型应用数量分布,统计展示平台中应用类型下包含的应用数量分布,4.0,发起者：管理员，接收者：密码应用数据管理系统,管理员请求查看密码应用类型应用数量分布,查看密码应用类型应用数量分布,读取密码应用类型应用数量分布数据,R,密码应用类型应用数量分布,类型名称、应用数量,1
,密码应用管理,密码应用分页列表查询,分页展示密码应用分页列表信息，列表内容：应用标识、应用名称、所属单位(机构；单租户显示)、业务描述、完整性校验,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击密码应用分页列表查询菜单,查看密码应用分页列表信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用分页数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述、完整性校验状态,1
,,密码应用过滤查询,支持应用标识、应用名称、简称进行过滤查询,3.0,发起者：管理员，接收者：密码应用管理系统,管理员在密码应用列表页面输入过滤条件,过滤查询密码应用信息,输入过滤条件,E,过滤条件,应用标识、应用名称、简称,1
,,,,,,,,读取过滤后的密码应用数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
,,新增密码应用,新增应用，录入内容：应用标识、应用名称、所属单位、业务类型（下拉多选）、业务类型对应的密码服务集群、认证方式（口令、AK/SK）、业务描述；,6.0,发起者：管理员，接收者：密码应用管理系统,管理员点击新增密码应用按钮并填写表单,新增密码应用,输入密码应用信息,E,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
,,,,,,,,保存密码应用信息,W,密码应用信息,应用标识、应用名称、所属单位、业务类型、密码服务集群、认证方式、业务描述,1
,,编辑密码应用,可编辑内容：应用名称、所属单位、业务描述,3.0,发起者：管理员，接收者：密码应用管理系统,管理员在密码应用详情页面点击编辑按钮,编辑密码应用信息,输入编辑后的密码应用信息,E,密码应用信息,应用名称、所属单位、业务描述,1
,,,,,,,,更新密码应用信息,W,密码应用信息,应用名称、所属单位、业务描述,1
,,删除密码应用,当应用下无密钥、无证书后，删除应用信息，同步删除应用认证方式和密码服务的调度关系等,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击删除密码应用按钮,删除密码应用,验证删除条件,R,密码应用关联信息,密钥数量、证书数量,1
,,,,,,,,输入删除请求,E,删除请求,应用标识,1
,,,,,,,,删除密码应用信息,W,密码应用信息,应用标识,1
,,,,,,,,同步删除认证方式和密码服务调度关系,X,关联信息,认证方式、密码服务集群,1
,,密码应用详情,展示密码应用的详细信息,4.0,发起者：管理员，接收者：密码应用管理系统,管理员点击密码应用详情按钮,查看密码应用详情,输入密码应用标识,E,查询条件,应用标识,1
,,,,,,,,读取密码应用详细信息,R,密码应用信息,应用标识、应用名称、所属单位、业务类型、认证方式、业务描述,1
,,密码应用信息完整性校验,保障密码应用的数据完整性，如数据被篡改，显示异常,3.0,发起者：系统，接收者：密码应用管理系统,系统定时校验密码应用数据完整性,校验密码应用数据完整性,读取密码应用数据,R,密码应用信息,应用标识、应用名称、所属单位、业务描述,1
,,,,,,,,校验数据完整性,X,校验结果,完整性状态、异常信息,1
,,应用认证凭证列表查询,展示密钥应用调用密码业务接口时使用的所有认证凭证列表,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击应用认证凭证列表查询菜单,查看应用认证凭证列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取认证凭证列表数据,R,认证凭证信息,认证凭证密钥ID、描述,1
,,应用认证凭证过滤查询,支持认证凭证密钥ID和描述过滤查询,3.0,发起者：管理员，接收者：密码应用管理系统,管理员在认证凭证列表页面输入过滤条件,过滤查询认证凭证,输入过滤条件,E,过滤条件,密钥ID、描述,1
,,,,,,,,读取过滤后的认证凭证数据,R,认证凭证信息,密钥ID、描述,1
,,新增应用认证凭证,选择认证方式，AK/SK ；创建成功后，自动下载对应SK文件；同步到认证中心，支持凭证的认证授权,5.0,发起者：管理员，接收者：密码应用管理系统,管理员点击新增认证凭证按钮并选择认证方式,新增应用认证凭证,选择认证方式,E,认证方式,AK/SK,1
,,,,,,,,生成SK文件,X,SK文件,SK内容,1
,,,,,,,,输入认证凭证信息,E,认证凭证信息,密钥ID、描述,1
,,,,,,,,保存认证凭证信息,W,认证凭证信息,认证方式、密钥ID、描述,1
,,,,,,,,同步认证凭证到认证中心,X,认证中心,认证方式、密钥ID,1
,,编辑应用认证凭证,编辑认证凭证的描述信息,4.0,发起者：管理员，接收者：密码应用管理系统,管理员在认证凭证详情页面点击编辑按钮,编辑认证凭证信息,输入编辑后的认证凭证信息,E,认证凭证信息,描述,1
,,,,,,,,更新认证凭证信息,W,认证凭证信息,描述,1
,,启用应用认证凭证,启用停用的认证凭证，通知认证中心，启用该凭证认证授权,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击启用认证凭证按钮,启用认证凭证,输入认证凭证标识,E,启用请求,认证凭证ID,1
,,,,,,,,通知认证中心启用凭证,X,认证中心,认证凭证ID,1
,,停用应用认证凭证,停用认证凭证，通知认证中心，禁止该凭证认证授权,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击停用认证凭证按钮,停用认证凭证,输入认证凭证标识,E,停用请求,认证凭证ID,1
,,,,,,,,通知认证中心停用凭证,X,认证中心,认证凭证ID,1
,,删除应用认证凭证,删除认证凭证，同步清除认证中心中的对应认证凭证,4.0,发起者：管理员，接收者：密码应用管理系统,管理员点击删除认证凭证按钮,删除认证凭证,输入认证凭证标识,E,删除请求,认证凭证ID,1
,,,,,,,,删除认证凭证信息,W,认证凭证信息,认证凭证ID,1
,,,,,,,,同步删除认证中心凭证,X,认证中心,认证凭证ID,1
,,应用认证凭证完整性校验,保障应用认证凭证的数据完整性，如数据被篡改，显示异常,3.0,发起者：系统，接收者：密码应用管理系统,系统定时校验认证凭证数据完整性,校验认证凭证数据完整性,读取认证凭证数据,R,认证凭证信息,密钥ID、描述,1
,,,,,,,,校验数据完整性,X,校验结果,完整性状态、异常信息,1
,,密码应用业务功能列表,处理应用业务和密码服务集群的绑定关系，安装服务类型进行绑定；列表展示内容：应用业务类型、密码服务集群名称,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击密码应用业务功能列表查询菜单,查看密码应用业务功能列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取业务功能绑定关系,R,业务功能绑定信息,应用业务类型、密码服务集群名称,1
,,新增密码应用业务功能,下拉选择业务类型、下拉选择密码服务集群,4.0,发起者：管理员，接收者：密码应用管理系统,管理员点击新增业务功能按钮并选择业务类型和集群,新增密码应用业务功能,选择业务类型和密码服务集群,E,业务功能绑定信息,业务类型、密码服务集群,1
,,,,,,,,保存业务功能绑定关系,W,业务功能绑定信息,业务类型、密码服务集群,1
,,删除密码应用业务功能,删除应用业务和密码服务集群的绑定关系,3.0,发起者：管理员，接收者：密码应用管理系统,管理员点击删除业务功能按钮,删除密码应用业务功能,输入业务类型和密码服务集群,E,删除请求,业务类型、密码服务集群,1
,,,,,,,,删除业务功能绑定关系,W,业务功能绑定信息,业务类型、密码服务集群,1
,密码应用场景管理,密码应用场景分页列表查询,展示密码应用场景信息，展示内容：序号、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用场景分页列表查询按钮,查看密码应用场景分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用场景数据,R,密码应用场景信息,序号、业务系统名称、所属应用、所在城市、是否主OMC、是否接入系统、算法、备注、创建时间,1
,,,,,,,,输出分页列表数据,X,分页列表数据,分页信息、密码应用场景信息,1
,,新建密码应用场景,新建密码应用场景，录入内容：业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,4.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击新建密码应用场景按钮并填写表单,新建密码应用场景,输入业务系统名称,E,业务系统信息,业务系统名称,1
,,,,,,,,输入应用信息,E,应用信息,应用,1
,,,,,,,,输入所在城市,E,地理位置信息,所在城市,1
,,,,,,,,输入是否主OMC标识,E,系统标识信息,是否主OMC,1
,,,,,,,,输入是否接入系统标识,E,接入状态信息,是否接入系统,1
,,,,,,,,输入算法类型,E,算法信息,算法,1
,,,,,,,,输入备注信息,E,备注信息,备注,1
,,,,,,,,保存新建密码应用场景,W,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,编辑密码应用场景,编辑密码应用场景，编辑内容：业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击编辑密码应用场景按钮并修改表单,编辑密码应用场景,选择待编辑的密码应用场景,E,场景标识信息,序号,1
,,,,,,,,读取当前密码应用场景数据,R,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,,,,,,,输入修改后的业务系统名称,E,业务系统信息,业务系统名称,1
,,,,,,,,输入修改后的应用信息,E,应用信息,应用,1
,,,,,,,,输入修改后的所在城市,E,地理位置信息,所在城市,1
,,,,,,,,输入修改后的是否主OMC标识,E,系统标识信息,是否主OMC,1
,,,,,,,,输入修改后的是否接入系统标识,E,接入状态信息,是否接入系统,1
,,,,,,,,输入修改后的算法类型,E,算法信息,算法,1
,,,,,,,,输入修改后的备注信息,E,备注信息,备注,1
,,,,,,,,保存编辑后的密码应用场景,W,密码应用场景信息,业务系统名称、应用、所在城市、是否主OMC、是否接入系统、算法、备注,1
,,删除密码应用场景,删除密码应用场景,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击删除密码应用场景按钮,删除密码应用场景,选择待删除的密码应用场景,E,场景标识信息,序号,1
,,,,,,,,执行密码应用场景删除操作,W,密码应用场景信息,序号,1
,密码应用改造厂商管理,密码应用改造厂商分页列表查询,展示参与密码应用改造的厂商信息,4.0,发起者：管理员，接收者：密码应用数据管理系统,管理员点击密码应用改造厂商分页列表查询按钮,查看密码应用改造厂商分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用改造厂商信息,R,密码应用改造厂商信息,厂商名称、联系方式、地址、服务内容,1
,,,,,,,,输出分页列表,X,分页列表数据,厂商名称、联系方式、地址、服务内容,1
,,新增密码应用改造厂商,新增密码应用改造的厂商信息,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用改造厂商管理页面点击新增按钮,新增密码应用改造厂商信息,输入厂商信息,E,密码应用改造厂商信息,厂商名称、联系方式、地址、服务内容,1
,,,,,,,,保存厂商信息,W,密码应用改造厂商信息,厂商名称、联系方式、地址、服务内容,1
,,编辑密码应用改造厂商,编辑密码应用改造的厂商信息,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用改造厂商管理页面点击编辑按钮,编辑密码应用改造厂商信息,选择待编辑厂商,E,密码应用改造厂商信息,厂商名称,1
,,,,,,,,输入修改后的厂商信息,E,密码应用改造厂商信息,厂商名称、联系方式、地址、服务内容,1
,,,,,,,,保存修改后的厂商信息,W,密码应用改造厂商信息,厂商名称、联系方式、地址、服务内容,1
,,删除密码应用改造厂商,删除密码应用改造的厂商信息,3.0,发起者：管理员，接收者：密码应用数据管理系统,管理员在密码应用改造厂商管理页面点击删除按钮,删除密码应用改造厂商信息,选择待删除厂商,E,密码应用改造厂商信息,厂商名称,1
,,,,,,,,执行删除操作,W,密码应用改造厂商信息,厂商名称,1
密码资产数据管理,密码资产名称管理,密码服务列表,平台中密码服务资源的列表信息，列表内容：序号、服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员访问密码服务列表页面,查看密码服务列表信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码服务列表数据,R,密码服务列表,序号、服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
,,密码服务查询,支持根据服务名称、服务类型、ip地址、端口进行检测,3.0,发起者：管理员，接收者：密码资产数据管理模块,管理员在密码服务列表页面点击查询按钮,查询密码服务信息,输入查询条件,E,查询条件,服务名称、服务类型、ip地址、端口,1
,,,,,,,,读取查询结果,R,密码服务列表,序号、服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
,,密码服务状态检测,定期通过rest接口检测密码服务状态,4.0,发起者：系统定时任务，接收者：密码资产数据管理模块,系统定时触发密码服务状态检测,检测密码服务状态,调用REST接口发送检测请求,X,检测请求,服务地址、检测类型,1
,,,,,,,,接收REST接口返回状态,R,检测结果,服务状态、响应时间,1
,,新建密码服务,新增密码服务资源，新增录入内容：服务名称、区域、服务类型、服务集群、设备集群、根据服务类型中部署类型决定展示服务规格还是ip端口、备注、创建数量,10.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击新建密码服务按钮,新增密码服务资源,输入基础服务信息,E,服务基础信息,服务名称、区域、服务类型、服务集群、设备集群、创建数量,1
,,,,,,,,输入服务规格或IP端口信息,E,服务规格信息,cpu核数、内存、管理ip、业务ip、管理端口、业务端口,1
,,,,,,,,保存密码服务资源,W,密码服务列表,服务名称、区域、服务类型、服务集群、设备集群、cpu核数、内存、管理ip、业务ip、管理端口、业务端口、创建数量、备注,1
,,编辑密码服务,编辑密码服务资源，编辑内容：服务名称、备注,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击编辑密码服务按钮,编辑密码服务资源,输入编辑信息,E,服务编辑信息,服务名称、备注,1
,,,,,,,,更新密码服务信息,W,密码服务列表,服务名称、备注,1
,,重启密码服务,重启密码服务容器,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击重启密码服务按钮,重启密码服务容器,发送重启指令,X,容器操作指令,容器标识、操作类型,1
,,,,,,,,接收重启结果,R,容器操作结果,操作状态、日志信息,1
,,启动密码服务,启动停止的密码服务容器,3.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击启动密码服务按钮,启动密码服务容器,发送启动指令,X,容器操作指令,容器标识、操作类型,1
,,,,,,,,接收启动结果,R,容器操作结果,操作状态、日志信息,1
,,停止密码服务,停止密码服务容器,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击停止密码服务按钮,停止密码服务容器,发送停止指令,X,容器操作指令,容器标识、操作类型,1
,,,,,,,,接收停止结果,R,容器操作结果,操作状态、日志信息,1
,,更新密码服务规格,修改密码服务运行容器的资源规格，实现单个密码服务的动态扩容、缩容,8.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击更新密码服务规格按钮,修改密码服务资源规格,输入资源规格参数,E,资源规格参数,cpu核数、内存,1
,,,,,,,,更新容器资源配置,W,密码服务列表,cpu核数、内存,1
,,删除密码服务,删除停止的密码服务,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击删除密码服务按钮,删除密码服务,确认删除操作,E,删除确认信息,服务标识、确认状态,1
,,,,,,,,执行删除操作,W,密码服务列表,服务标识,1
,,密码服务服务组新增,新增服务组，配置数据库,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击新增服务组按钮,新增密码服务服务组,输入服务组信息,E,服务组信息,服务组标识、服务组名称、业务类型,1
,,,,,,,,配置数据库连接,W,服务组数据库配置,数据库地址、数据库类型、连接参数,1
,,密码服务服务组列表,列表展示服务组信息，包括服务组标识、服务组名称、业务类型、服务数量等信息,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员访问服务组列表页面,查看服务组列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取服务组列表数据,R,服务组列表,服务组标识、服务组名称、业务类型、服务数量,1
,,密码服务服务组编辑,编辑服务组名称,2.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击编辑服务组按钮,编辑服务组信息,输入服务组名称,E,服务组信息,服务组名称,1
,,,,,,,,更新服务组信息,W,服务组列表,服务组名称,1
,,密码服务管理列表,查看服务组内的密码服务,2.0,发起者：管理员，接收者：密码资产数据管理模块,管理员访问服务组内密码服务页面,查看服务组内密码服务,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取服务组内密码服务数据,R,密码服务列表,服务名称、服务类型、所属租户、管理ip、业务ip、管理端口、业务端口、管控ip、管控端口、cpu核数、内存、服务集群、设备集群、镜像名称、镜像版本、容器名称、备注、创建时间、运行状态,1
,,密码服务释放,从服务组释放密码服务，,3.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击释放密码服务按钮,从服务组释放密码服务,选择释放的密码服务,E,服务选择信息,服务标识列表,1
,,,,,,,,更新服务组关联关系,W,服务组关联关系,服务标识列表,1
,,密码服务镜像列表,展示密码服务的镜像列表，包括文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注等信息,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员访问密码服务镜像列表页面,查看密码服务镜像列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取镜像列表数据,R,密码服务镜像列表,文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注,1
,,密码服务镜像上传,上传密码服务镜像并校验文件摘要,5.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击上传密码服务镜像按钮,上传密码服务镜像,选择镜像文件,E,镜像文件信息,文件名称、文件内容,1
,,,,,,,,校验文件摘要,R,校验结果,校验状态、摘要值,1
,,,,,,,,保存镜像文件,W,密码服务镜像列表,文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注,1
,,密码服务镜像编辑,编辑备注信息,2.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击编辑镜像备注按钮,编辑密码服务镜像备注,输入新备注信息,E,镜像备注信息,备注内容,1
,,,,,,,,更新镜像备注,W,密码服务镜像列表,备注,1
,,密码服务镜像查询,根据镜像名称、服务类型进行查询,2.0,发起者：管理员，接收者：密码资产数据管理模块,管理员在镜像列表页面点击查询按钮,查询密码服务镜像,输入查询条件,E,查询条件,镜像名称、服务类型,1
,,,,,,,,读取查询结果,R,密码服务镜像列表,文件名称、服务类型、镜像名称、镜像版本、文件大小、完整性、状态、备注,1
,,密码服务镜像启用,启用密码服务镜像,2.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击启用镜像按钮,启用密码服务镜像,选择启用的镜像,E,镜像选择信息,镜像标识,1
,,,,,,,,更新镜像状态,W,密码服务镜像列表,状态,1
,,密码服务镜像禁用,禁用密码服务镜像,2.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击禁用镜像按钮,禁用密码服务镜像,选择禁用的镜像,E,镜像选择信息,镜像标识,1
,,,,,,,,更新镜像状态,W,密码服务镜像列表,状态,1
,,密码服务镜像删除,删除密码服务镜像,4.0,发起者：管理员，接收者：密码资产数据管理模块,管理员点击删除镜像按钮,删除密码服务镜像,确认删除操作,E,删除确认信息,镜像标识、确认状态,1
,,,,,,,,执行删除操作,W,密码服务镜像列表,镜像标识,1
,密码资产数据管理,密码服务数据库新增,新增数据库信息，选择数据库类型，输入数据库IP、端口，管理员账号、密码,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击密码服务数据库新增按钮,新增密码服务数据库信息,选择数据库类型,E,数据库类型信息,数据库类型名称,1
,,,,,,,,输入数据库IP和端口,E,数据库连接信息,数据库IP、端口,1
,,,,,,,,输入管理员账号和密码,E,数据库认证信息,管理员账号、密码,1
,,,,,,,,保存数据库信息,W,数据库配置信息,数据库类型、IP、端口、管理员账号、密码,1
,,密码服务数据库列表,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4.0,发起者：管理员，接收者：密码资产管理系统,管理员访问密码服务数据库列表页面,查看密码服务数据库列表,查询数据库信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取数据库列表信息,R,数据库列表信息,数据库名称、类型、实例库名称、IP、端口、完整性校验状态,1
,,密码服务数据库模式列表,列表展示密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员点击密码服务数据库模式列表按钮,查看密码服务数据库模式列表,查询数据库模式信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取数据库模式列表信息,R,数据库模式信息,模式名称、创建时间、关联数据库,1
,,密码服务数据库模式删除,删除密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在数据库模式列表页面点击删除按钮,删除密码服务数据库模式,选择待删除的数据库模式,E,数据库模式信息,模式名称,1
,,,,,,,,确认删除操作,E,确认信息,确认删除,1
,,,,,,,,执行数据库模式删除,W,数据库模式信息,模式名称,1
,,密码服务数据库模式查询,查询密码服务数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员在数据库模式列表页面点击新增按钮,新增密码服务数据库模式,输入数据库模式名称,E,数据库模式信息,模式名称,1
,,,,,,,,关联数据库,E,数据库关联信息,关联数据库名称,1
,,,,,,,,保存数据库模式信息,W,数据库模式信息,模式名称、关联数据库,1
,,密码服务数据库模式新增,新增数据库模式,2.0,发起者：管理员，接收者：密码资产管理系统,管理员点击API网关新增按钮,新增API网关信息,输入网关名称,E,API网关信息,网关名称,1
,,,,,,,,选择所属需求,E,需求信息,需求名称,1
,,,,,,,,输入网关标识,E,API网关信息,网关标识,1
,,,,,,,,选择网关类型,E,网关类型信息,网关类型,1
,,,,,,,,输入管理端口,E,API网关信息,管理端口,1
,,,,,,,,保存API网关信息,W,API网关信息,网关名称、所属需求、标识、类型、管理端口,1
,,API网关列表,列表内容：名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP（列表不显示）、端口（列表不显示）、反向代理地址端口（列表不显示）,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在API网关列表页面点击编辑按钮,编辑API网关信息,选择待编辑的API网关,E,API网关信息,网关名称,1
,,,,,,,,修改网关名称,E,API网关信息,网关名称,1
,,,,,,,,修改管理端口,E,API网关信息,管理端口,1
,,,,,,,,保存API网关信息,W,API网关信息,网关名称、管理端口,1
,,API网关初始化,密码服务平台部署成功后，如选择部署API网关，根据平台部署信息，自动加载对应的部署网关信息,3.0,发起者：管理员，接收者：密码资产管理系统,管理员在API网关列表页面点击删除按钮,删除API网关信息,选择待删除的API网关,E,API网关信息,网关名称,1
,,,,,,,,确认删除操作,E,确认信息,确认删除,1
,,,,,,,,执行API网关删除,W,API网关信息,网关名称,1
,,API网关新增,新增API网关信息，录入名称、所属需求、标识、类型（管理、业务）、管理端口,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击设备类型新增按钮,新增设备类型信息,输入设备类型名称,E,设备类型信息,设备类型名称,1
,,,,,,,,选择所属厂商,E,厂商信息,厂商名称,1
,,,,,,,,选择设备类型（云密码机/物理密码机/虚拟密码机）,E,设备类型信息,设备类型,1
,,,,,,,,选择管理接口协同方式,E,接口信息,管理接口类型,1
,,,,,,,,输入管理端口,E,设备类型信息,管理端口,1
,,,,,,,,保存设备类型信息,W,设备类型信息,设备类型名称、厂商、设备类型、管理接口类型、管理端口,1
,,API网关编辑,编辑内容：网关名称、管理端口,nan,发起者：管理员，接收者：密码资产管理系统,管理员在设备类型列表页面点击编辑按钮,编辑设备类型信息,选择待编辑的设备类型,E,设备类型信息,设备类型名称,1
,,,,,,,,修改设备类型名称,E,设备类型信息,设备类型名称,1
,,,,,,,,修改所属厂商,E,厂商信息,厂商名称,1
,,,,,,,,修改设备类型,E,设备类型信息,设备类型,1
,,,,,,,,修改管理接口协同方式,E,接口信息,管理接口类型,1
,,,,,,,,修改管理端口,E,设备类型信息,管理端口,1
,,,,,,,,保存设备类型信息,W,设备类型信息,设备类型名称、厂商、设备类型、管理接口类型、管理端口,1
,,API网关删除,删除网关信息,3.0,发起者：管理员，接收者：密码资产管理系统,管理员在设备类型列表页面点击删除按钮,删除设备类型信息,选择待删除的设备类型,E,设备类型信息,设备类型名称,1
,,,,,,,,确认删除操作,E,确认信息,确认删除,1
,,,,,,,,执行设备类型删除,W,设备类型信息,设备类型名称,1
,,路由管理列表,展示内容：路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击密码设备集群新增按钮,新增密码设备集群信息,输入集群名称,E,集群信息,集群名称,1
,,,,,,,,选择设备类型,E,设备类型信息,设备类型名称,1
,,,,,,,,选择所属区域,E,区域信息,区域名称,1
,,,,,,,,输入集群描述,E,集群信息,描述,1
,,,,,,,,保存集群信息,W,集群信息,集群名称、设备类型、所属区域、描述,1
,,路由管理详情,展示路由管理详情，包含服务列表信息、应用信息,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在密码设备集群列表页面点击编辑按钮,编辑密码设备集群信息,选择待编辑的集群,E,集群信息,集群名称,1
,,,,,,,,修改集群名称,E,集群信息,集群名称,1
,,,,,,,,修改集群描述,E,集群信息,描述,1
,,,,,,,,保存集群信息,W,集群信息,集群名称、描述,1
,,设备类型展示,内容：设备类型名称、所属厂商（录入）、设备类型（云密码机、物理密码机、虚拟密码机）、管理接口协同（HTTPS、HTTP）、管理端口；云密码机、虚拟密码机、物理密码机类型配置信息不同,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在密码设备集群列表页面点击删除按钮,删除密码设备集群信息,选择待删除的集群,E,集群信息,集群名称,1
,,,,,,,,确认删除操作,E,确认信息,确认删除,1
,,,,,,,,执行集群删除,W,集群信息,集群名称,1
,,设备类型初始化,根据平台支持的设备类型，平台部署时，初始化平台默认支持的设备类型,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击云密码机新建按钮,新建云密码机信息,输入云密码机名称,E,云密码机信息,云密码机名称,1
,,,,,,,,输入管理IP,E,云密码机信息,管理IP,1
,,,,,,,,输入管理端口,E,云密码机信息,管理端口,1
,,,,,,,,输入备注信息,E,云密码机信息,备注,1
,,,,,,,,保存云密码机信息,W,云密码机信息,云密码机名称、管理IP、管理端口、备注,1
,,设备类型新增,添加设备对应信息,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机列表页面点击编辑按钮,编辑云密码机信息,选择待编辑的云密码机,E,云密码机信息,云密码机名称,1
,,,,,,,,修改云密码机名称,E,云密码机信息,云密码机名称,1
,,,,,,,,修改备注信息,E,云密码机信息,备注,1
,,,,,,,,保存云密码机信息,W,云密码机信息,云密码机名称、备注,1
,,设备类型编辑,编辑设备相关信息,4.0,发起者：管理员，接收者：密码资产管理系统,管理员在云密码机列表页面点击删除按钮,删除云密码机信息,选择待删除的云密码机,E,云密码机信息,云密码机名称,1
,,,,,,,,确认删除操作,E,确认信息,确认删除,1
,,,,,,,,执行云密码机删除,W,云密码机信息,云密码机名称,1
,,设备类型停用,停用设备类型不可再创建该类型的设备,2.0,发起者：管理员，接收者：密码资产管理系统,管理员点击新增虚拟机网络配置按钮,新增虚拟机网络配置,输入管理IP范围,E,网络配置信息,管理IP范围,1
,,,,,,,,输入业务IP范围,E,网络配置信息,业务IP范围,1
,,,,,,,,保存网络配置信息,W,网络配置信息,管理IP范围、业务IP范围,1
,,设备类型启用,启用停用的设备类型,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击批量创建虚拟密码机按钮,批量创建虚拟密码机,选择云密码机,E,云密码机信息,云密码机名称,1
,,,,,,,,配置虚拟机资源,E,资源信息,CPU、内存、存储,1
,,,,,,,,加载虚拟机网络配置,R,网络配置信息,管理IP范围、业务IP范围,1
,,,,,,,,创建虚拟密码机,W,虚拟密码机信息,虚拟密码机名称、云密码机、IP、端口,1
,,设备类型删除,当无该类型的设备时，删除对应设备类型,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击生成虚机影像按钮,生成虚拟机影像,选择虚拟密码机,E,虚拟密码机信息,虚拟密码机名称,1
,,,,,,,,生成影像文件,X,影像文件,影像文件名称、大小,1
,,监控信息配置查看,查询当前监控信息的配置信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击下载虚机影像按钮,下载虚拟机影像,选择待下载的影像文件,E,影像文件,影像文件名称,1
,,,,,,,,执行影像文件下载,X,影像文件,影像文件名称、大小,1
,,监控信息配置,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击导入虚机影像按钮,导入虚拟机影像,选择影像文件,E,影像文件,影像文件名称,1
,,,,,,,,执行影像文件导入,W,影像文件,影像文件名称、大小,1
,,密码设备集群列表,列表内容：名称、设备类型、所属区域、设备数量、描述,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击物理密码机新建按钮,新建物理密码机信息,输入物理密码机名称,E,物理密码机信息,物理密码机名称,1
,,,,,,,,选择所属厂商,E,厂商信息,厂商名称,1
,,,,,,,,选择设备类型,E,设备类型信息,设备类型名称,1
,,,,,,,,输入管理IP,E,物理密码机信息,管理IP,1
,,,,,,,,输入管理端口,E,物理密码机信息,管理端口,1
,,,,,,,,输入版本信息,E,物理密码机信息,版本,1
,,,,,,,,输入序列号,E,物理密码机信息,序列号,1
,,,,,,,,输入备注信息,E,物理密码机信息,备注,1
,,,,,,,,保存物理密码机信息,W,物理密码机信息,物理密码机名称、厂商、设备类型、管理IP、管理端口、版本、序列号、备注,1
,,密码设备集群新增,创建密码设备集群，录入名称、选择设备类型、所属区域、描述,3.0,发起者：管理员，接收者：密码资产管理系统,管理员在物理密码机列表页面点击编辑按钮,编辑物理密码机信息,选择待编辑的物理密码机,E,物理密码机信息,物理密码机名称,1
,,,,,,,,修改物理密码机名称,E,物理密码机信息,物理密码机名称,1
,,,,,,,,修改备注信息,E,物理密码机信息,备注,1
,,,,,,,,修改连接密码,E,物理密码机信息,连接密码,1
,,,,,,,,保存物理密码机信息,W,物理密码机信息,物理密码机名称、备注、连接密码,1
,,密码设备集群编辑,可编辑内容：名称、描述,3.0,发起者：管理员，接收者：密码资产管理系统,管理员在物理密码机列表页面点击删除按钮,删除物理密码机信息,选择待删除的物理密码机,E,物理密码机信息,物理密码机名称,1
,,,,,,,,确认删除操作,E,确认信息,确认删除,1
,,,,,,,,执行物理密码机删除,W,物理密码机信息,物理密码机名称,1
,,密码设备集群删除,删除密码设备集群，需保障密码设备集群未被密码服务调用,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥创建按钮,创建保护主密钥,选择设备类型,E,设备类型信息,设备类型名称,1
,,,,,,,,输入密钥信息,E,密钥信息,密钥名称、算法类型、密钥长度,1
,,,,,,,,生成保护主密钥,X,密钥信息,密钥名称、算法类型、密钥长度,1
,,,,,,,,保存保护主密钥信息,W,密钥信息,密钥名称、算法类型、密钥长度,1
,,绑定密码设备,根据密码设备类型绑定密码设备，绑定密码设备后，根据类型配置判断是否需要进行保护密钥的创建和同步,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥同步按钮,同步保护主密钥,选择待同步的密钥,E,密钥信息,密钥名称,1
,,,,,,,,读取现有密钥信息,R,密钥信息,密钥名称、算法类型、密钥长度,1
,,,,,,,,执行密钥同步,W,密钥信息,密钥名称、算法类型、密钥长度,1
,,释放密码设备,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥备份按钮,备份保护主密钥,选择待备份的密钥,E,密钥信息,密钥名称,1
,,,,,,,,执行密钥加密导出,X,密钥备份文件,密钥名称、加密算法,1
,,云密码机列表,云密码机列表页面中，在搜索框中输入名称和管理IP，可以模糊查询云密码机列表,4.0,发起者：管理员，接收者：密码资产管理系统,管理员点击保护主密钥还原按钮,还原保护主密钥,选择密钥备份文件,E,密钥备份文件,备份文件名称,1
,,,,,,,,执行密钥还原,W,密钥信息,密钥名称、算法类型、密钥长度,1
,密码产品证书及编号管理,用户证书导入,导出用户证书，支持传入签名证书和加密证书。,6.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在证书管理页面点击用户证书导入按钮,导入用户证书,输入用户证书信息,E,用户证书信息,证书名称、证书内容、签名证书、加密证书,1
,,,,,,,,验证证书格式和有效性,R,证书校验规则,证书格式、有效期、签名算法,1
,,,,,,,,保存用户证书,W,用户证书信息,证书名称、证书内容、签名证书、加密证书,1
,,用户证书列表,列表分页展示用户证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击分页查询按钮,分页展示用户证书列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取用户证书列表,R,用户证书信息,证书名称、证书状态、创建时间,1
,,,,,,,,输出分页结果,X,分页证书列表,证书名称、证书状态、创建时间,1
,,用户证书停用,停用用户证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击停用按钮,停用用户证书,选择证书,E,证书选择,证书ID,1
,,,,,,,,更新证书状态,W,用户证书信息,证书ID、状态,1
,,用户证书启用,启用用户证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击启用按钮,启用用户证书,选择证书,E,证书选择,证书ID,1
,,,,,,,,更新证书状态,W,用户证书信息,证书ID、状态,1
,,用户证书删除,删除用户证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在用户证书管理页面点击删除按钮,删除用户证书,选择证书,E,证书选择,证书ID,1
,,,,,,,,删除证书记录,W,用户证书信息,证书ID,1
,,应用证书创建,创建应用证书,5.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击创建按钮,创建应用证书,输入应用证书信息,E,应用证书信息,证书名称、证书类型、有效期,1
,,,,,,,,生成证书请求,W,证书请求,证书名称、证书类型、有效期,1
,,下载应用证书证书请求,下载应用证书证书请求,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击下载证书请求按钮,下载应用证书请求,选择证书请求,E,证书请求选择,请求ID,1
,,,,,,,,输出证书请求文件,X,证书请求文件,请求内容,1
,,应用证书导入,根据证书请求导入应用证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击导入按钮,导入应用证书,上传证书请求文件,E,证书请求文件,请求内容,1
,,,,,,,,验证证书请求,R,证书校验规则,请求格式、签名算法,1
,,,,,,,,生成应用证书,W,应用证书信息,证书名称、证书内容,1
,,导入应用证书和密钥,直接导入应用证书，包括签名证书、证书口令、加密证书、加密私钥,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击直接导入按钮,直接导入应用证书,输入证书和密钥信息,E,证书密钥信息,签名证书、证书口令、加密证书、加密私钥,1
,,,,,,,,验证证书和密钥有效性,R,证书校验规则,证书格式、密钥匹配性,1
,,,,,,,,保存证书和密钥,W,应用证书信息,签名证书、证书口令、加密证书、加密私钥,1
,,应用证书列表查询,分页展示应用证书列表,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击分页查询按钮,分页展示应用证书列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取应用证书列表,R,应用证书信息,证书名称、证书状态、创建时间,1
,,,,,,,,输出分页结果,X,分页证书列表,证书名称、证书状态、创建时间,1
,,应用证书停用,停用应用证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击停用按钮,停用应用证书,选择证书,E,证书选择,证书ID,1
,,,,,,,,更新证书状态,W,应用证书信息,证书ID、状态,1
,,应用证书启用,启用应用证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击启用按钮,启用应用证书,选择证书,E,证书选择,证书ID,1
,,,,,,,,更新证书状态,W,应用证书信息,证书ID、状态,1
,,应用证书删除,删除应用证书,3.0,发起者：管理员，接收者：密码资产数据管理系统,管理员在应用证书管理页面点击删除按钮,删除应用证书,选择证书,E,证书选择,证书ID,1
,,,,,,,,删除证书记录,W,应用证书信息,证书ID,1
,密钥信息管理,新增密钥,新增密钥，支持创建对称密钥和非对称密钥。支持3DES、AES、SM4、SM2、SM9、RSA、ZUC等密钥算法,10.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击新增密钥按钮,创建对称/非对称密钥,输入密钥基本信息,E,密钥基础信息,密钥名称、密钥类型（对称/非对称）、密钥算法（3DES、AES、SM4、SM2、SM9、RSA、ZUC）,1
,,,,,,,,选择密钥长度,E,密钥参数,密钥长度,1
,,,,,,,,保存密钥配置,W,密钥信息,密钥名称、密钥类型、密钥算法、密钥长度,1
,,密钥信息列表,列表分页展示密钥信息，包括应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度等信息。,4.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员访问密钥信息列表页面,分页展示密钥信息,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密钥列表数据,R,密钥信息列表,应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度,1
,,密钥查询,根据应用、密钥id、密钥名称查询密钥,2.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员在密钥列表页面点击查询按钮,根据条件查询密钥,输入查询条件,E,查询参数,应用名称、密钥id、密钥名称,1
,,,,,,,,读取查询结果,R,密钥信息列表,应用名称、密钥id、密钥名称、密钥类型、密钥状态、密钥算法、密钥长度,1
,,密钥详情,展示密钥详情信息，包括密钥摘要值，密钥来源、密钥用途、是否可导出等,4.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥详情按钮,展示密钥详情信息,读取密钥详情数据,R,密钥详情,密钥摘要值、密钥来源、密钥用途、是否可导出,1
,,密钥链接,查看密钥的密钥链接信息,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥链接信息按钮,查看密钥链接信息,读取密钥链接数据,R,密钥链接信息,关联密钥id、关联密钥名称、关联时间,1
,,密钥历史版本,支持查看密钥的历史版本信息,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥历史版本按钮,查看密钥历史版本,读取历史版本数据,R,密钥历史版本,版本号、创建时间、操作人、版本状态,1
,,密钥翻新,将密钥做翻新处理,4.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥翻新按钮,执行密钥翻新操作,输入翻新参数,E,翻新参数,新密钥算法、新密钥长度,1
,,,,,,,,执行密钥翻新,W,密钥信息,密钥算法、密钥长度、翻新时间,1
,,密钥自动翻新,根据自动翻新策略，自动翻新密钥,4.0,发起者：系统，接收者：密码资产数据管理-密钥信息管理模块,系统根据自动翻新策略触发,自动翻新密钥,读取自动翻新策略,R,自动翻新策略,触发条件、执行频率,1
,,,,,,,,执行自动翻新,W,密钥信息,密钥算法、密钥长度、翻新时间,1
,,密钥归档,将密钥做归档处理,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥归档按钮,执行密钥归档操作,输入归档参数,E,归档参数,归档原因、归档时间,1
,,,,,,,,更新密钥状态,W,密钥信息,密钥状态（归档）,1
,,密钥恢复,将密钥做归档恢复,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥恢复按钮,执行密钥归档恢复,输入恢复参数,E,恢复参数,恢复原因、恢复时间,1
,,,,,,,,更新密钥状态,W,密钥信息,密钥状态（恢复）,1
,,密钥注销,将密钥做注销处理,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥注销按钮,执行密钥注销操作,输入注销参数,E,注销参数,注销原因、注销时间,1
,,,,,,,,更新密钥状态,W,密钥信息,密钥状态（注销）,1
,,密钥销毁,将密钥做销毁处理,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥销毁按钮,执行密钥销毁操作,输入销毁参数,E,销毁参数,销毁原因、销毁时间,1
,,,,,,,,删除密钥数据,W,密钥信息,密钥数据、销毁状态,1
,,密钥删除,将密钥做删除处理,3.0,发起者：管理员，接收者：密码资产数据管理-密钥信息管理模块,管理员点击密钥删除按钮,执行密钥删除操作,输入删除参数,E,删除参数,删除原因、删除时间,1
,,,,,,,,删除密钥记录,W,密钥信息,密钥id、删除状态,1
,密码文档信息管理,添加密码知识库数据,平台操作员登录，可上传文件添加一条知识库记录,4.0,发起者：平台操作员，接收者：密码资产数据管理模块,平台操作员登录后上传文件添加知识库记录,添加密码知识库数据,输入文件信息,E,文件信息,文件名、文件类型、文档分类、文件大小,1
,,,,,,,,读取上传文件内容,R,上传文件,文件二进制数据、文件元数据,1
,,,,,,,,保存知识库记录,W,知识库记录,文件名、文件类型、文档分类、文件路径、上传时间,1
,,编辑密码知识库数据,平台操作员登录，可编辑知识库记录,3.0,发起者：平台操作员，接收者：密码资产数据管理模块,平台操作员在知识库记录页面点击编辑按钮,编辑密码知识库数据,选择待编辑记录,E,知识库记录,记录ID、文件名,1
,,,,,,,,输入更新后的文件信息,E,文件信息,文件名、文件类型、文档分类,1
,,,,,,,,更新知识库记录,W,知识库记录,文件名、文件类型、文档分类、更新时间,1
,,删除密码知识库数据,"删除密码知识库数据
,平台操作员登录点击密码知识库菜单，点击数据后删除，删除该条记录以及文件
",3.0,发起者：平台操作员，接收者：密码资产数据管理模块,平台操作员点击删除按钮删除知识库记录,删除密码知识库数据,选择待删除记录,E,知识库记录,记录ID、文件名,1
,,,,,,,,删除关联文件,X,存储文件,文件路径、文件名,1
,,,,,,,,删除知识库记录,W,知识库记录,记录ID,1
,,查询密码知识库数据,查询所有密码知识库记录，可根据文件类型、文档分类、文件名筛选,3.0,发起者：平台操作员，接收者：密码资产数据管理模块,平台操作员输入查询条件查询知识库记录,查询密码知识库数据,输入查询条件,E,查询条件,文件类型、文档分类、文件名,1
,,,,,,,,读取匹配的知识库记录,R,知识库记录,文件名、文件类型、文档分类、上传时间,1
,,,,,,,,输出查询结果,X,查询结果,文件列表、分页信息,1
,,显示/隐藏知识库信息,点击显示/隐藏可配置是否为租户显示该条记录,3.0,发起者：平台操作员，接收者：密码资产数据管理模块,平台操作员点击显示/隐藏按钮配置记录可见性,配置知识库信息显示状态,选择记录和显示状态,E,显示配置,记录ID、显示状态（显示/隐藏）,1
,,,,,,,,更新显示配置,W,知识库记录,显示状态、更新时间,1
,,预览知识库信息,点击预览可预览知识库信息，支持文档、表格、pdf、视频、音频,3.0,发起者：平台操作员，接收者：密码资产数据管理模块,平台操作员点击预览按钮查看知识库文件,预览知识库信息,选择预览文件,E,知识库记录,记录ID、文件名,1
,,,,,,,,读取文件内容,R,存储文件,文件二进制数据、文件类型,1
,,,,,,,,输出预览内容,X,预览内容,文档内容、文件类型标识,1
密码应用测评管理,改造阶段管理,密码应用测评改造阶段分页列表,展示密码应用测评改造过程中划分的不同阶段信息,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击改造阶段分页列表菜单,查看密码应用测评改造阶段分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取改造阶段分页数据,R,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,密码应用测评改造阶段过滤查询,支持根据阶段编码、阶段名称过滤,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在改造阶段列表页面输入过滤条件,过滤查询密码应用测评改造阶段,输入过滤条件,E,过滤条件,阶段编码、阶段名称,1
,,,,,,,,读取过滤后的改造阶段数据,R,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,新增密码应用测评改造阶段,新增测评改造过程中的阶段信息,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击新增改造阶段按钮并填写信息,新增密码应用测评改造阶段,输入新增阶段信息,E,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,,,,,,,写入新增阶段信息,W,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,编辑密码应用测评改造阶段,编辑测评改造过程中的阶段信息,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在改造阶段列表中点击编辑按钮并修改信息,编辑密码应用测评改造阶段,读取待编辑阶段信息,R,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,,,,,,,输入修改后的阶段信息,E,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,,,,,,,写入修改后的阶段信息,W,改造阶段信息,阶段编码、阶段名称、阶段描述、阶段状态,1
,,删除密码应用测评改造阶段,删除测评改造过程中的阶段信息,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在改造阶段列表中点击删除按钮,删除密码应用测评改造阶段,读取待删除阶段信息,R,改造阶段信息,阶段编码、阶段名称,1
,,,,,,,,写入删除操作,W,改造阶段信息,阶段编码,1
,,密码应用设置测评改造阶段,设置密码应用当前处于的测评阶段,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用配置页面设置当前测评阶段,设置密码应用当前测评阶段,输入当前阶段信息,E,当前阶段信息,密码应用ID、阶段编码,1
,,,,,,,,写入当前阶段信息,W,当前阶段信息,密码应用ID、阶段编码,1
,,密码应用修改测评改造阶段,修改密码应用当前处于的测评阶段,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用配置页面修改当前测评阶段,修改密码应用当前测评阶段,读取当前阶段信息,R,当前阶段信息,密码应用ID、阶段编码,1
,,,,,,,,输入修改后的阶段信息,E,当前阶段信息,密码应用ID、阶段编码,1
,,,,,,,,写入修改后的当前阶段信息,W,当前阶段信息,密码应用ID、阶段编码,1
,,查询密码应用测评改造阶段,查询密码应用当前处于的测评阶段,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用配置页面查询当前测评阶段,查询密码应用当前测评阶段,读取当前阶段信息,R,当前阶段信息,密码应用ID、阶段编码、阶段名称,1
,,测评改造阶段的应用分布,根据测评改造阶段查询包含的应用数量,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在改造阶段统计页面查询应用分布,查询测评改造阶段的应用分布,输入阶段查询条件,E,阶段查询条件,阶段编码、阶段名称,1
,,,,,,,,读取应用分布数据,R,应用分布信息,阶段名称、应用数量,1
,应用测评报告、测评分数管理,应用测评报告分页列表查询,应用测评报告列表展示，展示内容：序号、报告名称、所属应用、报告格式、测评分数、上报时间、操作,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评报告分页列表查询按钮,查询并展示应用测评报告分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取分页参数,R,分页信息,页码、单页数量,1
,,,,,,,,查询测评报告列表数据,R,测评报告列表,报告名称、所属应用、报告格式、测评分数、上报时间,1
,,,,,,,,输出分页后的测评报告列表,X,测评报告列表,序号、报告名称、所属应用、报告格式、测评分数、上报时间、操作,1
,,新增应用测评报告对象,新增测评报告数据对象，录入内容：报告名称、所属应用、报告格式、测评分数,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击新增应用测评报告对象按钮并填写表单,新增测评报告数据对象,输入测评报告基本信息,E,测评报告对象,报告名称、所属应用、报告格式、测评分数,1
,,,,,,,,校验测评报告基本信息,R,校验规则,字段格式、唯一性约束,1
,,,,,,,,写入测评报告对象数据,W,测评报告对象,报告名称、所属应用、报告格式、测评分数,1
,,,,,,,,输出新增成功提示,X,操作反馈,成功状态、提示信息,1
,,应用测评报告文件上传,上传测评报告文件到文件服务，并绑定到测评报告对象,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评报告文件上传按钮并选择文件,上传测评报告文件并绑定,输入文件元数据,E,文件信息,文件名称、文件类型、文件大小,1
,,,,,,,,上传文件到文件服务,W,文件服务,文件流、存储路径,1
,,,,,,,,绑定文件到测评报告对象,W,测评报告对象,文件存储路径、文件名称,1
,,,,,,,,输出上传完成状态,X,操作反馈,成功状态、文件路径,1
,,应用测评报告文件预览,页面预览上传的测评报告文件,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评报告文件预览按钮,预览测评报告文件,读取文件存储路径,R,测评报告对象,文件存储路径,1
,,,,,,,,从文件服务读取文件内容,R,文件服务,文件流,1
,,,,,,,,输出文件预览内容,X,预览内容,文件内容、格式类型,1
,,应用测评报告文件下载,选择测评报告对象，下载上传的测评报告信息,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击应用测评报告文件下载按钮,下载测评报告文件,选择测评报告对象,E,测评报告对象,报告ID,1
,,,,,,,,读取文件存储路径,R,测评报告对象,文件存储路径,1
,,,,,,,,从文件服务读取文件内容,R,文件服务,文件流,1
,,,,,,,,输出文件下载流,X,下载文件,文件流、文件名称,1
,,编辑应用测评报告对象,编辑测评报告数据对象，修改名称和描述,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击编辑应用测评报告对象按钮并修改内容,编辑测评报告数据对象,读取原始测评报告数据,R,测评报告对象,报告名称、所属应用、报告格式、测评分数,1
,,,,,,,,输入修改后的测评报告数据,E,测评报告对象,报告名称、所属应用、报告格式、测评分数,1
,,,,,,,,校验修改后的数据,R,校验规则,字段格式、唯一性约束,1
,,,,,,,,写入更新后的测评报告数据,W,测评报告对象,报告名称、所属应用、报告格式、测评分数,1
,,,,,,,,输出编辑成功提示,X,操作反馈,成功状态、提示信息,1
,,删除应用测评报告对象,删除测评报告数据和对应文件,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击删除应用测评报告对象按钮,删除测评报告数据及文件,选择测评报告对象,E,测评报告对象,报告ID,1
,,,,,,,,读取文件存储路径,R,测评报告对象,文件存储路径,1
,,,,,,,,删除文件服务中的文件,W,文件服务,文件存储路径,1
,,,,,,,,删除测评报告数据,W,测评报告对象,报告ID,1
,,,,,,,,输出删除成功提示,X,操作反馈,成功状态、提示信息,1
,密码应用方案管理,密码应用测评方案分页列表,查看到密码应用测评方案的列表，分页展示,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击密码应用测评方案分页列表菜单,查看密码应用测评方案分页列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码应用测评方案数据,R,密码应用测评方案信息,应用名称、等保等级、等保状态、模板名称,1
,,,,,,,,输出分页列表数据,X,分页列表数据,测评方案列表、总记录数,1
,,新建密码应用测评方案,"为某个应用新建一个密码应用测评；用户进入密码应用测评方案界面，点击新增按钮，选择应用，输入等保等级，等保状态等相关数据，选择密评进度模版、密评要求模版、密评交付模版，点击新增。
",4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密码应用测评方案界面点击新增按钮，输入测评方案信息,新建密码应用测评方案,选择应用,E,应用信息,应用名称、应用ID,1
,,,,,,,,输入等保等级和等保状态,E,等保信息,等保等级、等保状态,1
,,,,,,,,选择密评进度模板,E,模板信息,进度模板名称、模板ID,1
,,,,,,,,选择密评要求模板,E,模板信息,要求模板名称、模板ID,1
,,,,,,,,选择密评交付模板,E,模板信息,交付模板名称、模板ID,1
,,,,,,,,保存测评方案,W,密码应用测评方案信息,应用ID、等保等级、等保状态、进度模板ID、要求模板ID、交付模板ID,1
,,绑定密码应用测评进度模板,将密码应用测评方案对象绑定测评进度模板,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案详情页点击绑定进度模板按钮,绑定密码应用测评进度模板,选择进度模板,E,模板信息,进度模板ID、模板名称,1
,,,,,,,,保存模板绑定关系,W,模板绑定信息,测评方案ID、进度模板ID,1
,,绑定密码应用测评要求模板,将密码应用测评方案对象绑定测评要求模板,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案详情页点击绑定要求模板按钮,绑定密码应用测评要求模板,选择要求模板,E,模板信息,要求模板ID、模板名称,1
,,,,,,,,保存模板绑定关系,W,模板绑定信息,测评方案ID、要求模板ID,1
,,密码应用测评进度模板编辑,编辑密码应用测评方案对象绑定的测评进度模板,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案详情页点击编辑进度模板按钮,编辑密码应用测评进度模板,读取当前绑定的进度模板,R,模板绑定信息,测评方案ID、进度模板ID,1
,,,,,,,,选择新的进度模板,E,模板信息,进度模板ID、模板名称,1
,,,,,,,,更新模板绑定关系,W,模板绑定信息,测评方案ID、进度模板ID,1
,,密码应用测评要求模板编辑,编辑密码应用测评方案对象绑定的测评要求模板,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评方案详情页点击编辑要求模板按钮,编辑密码应用测评要求模板,读取当前绑定的要求模板,R,模板绑定信息,测评方案ID、要求模板ID,1
,,,,,,,,选择新的要求模板,E,模板信息,要求模板ID、模板名称,1
,,,,,,,,更新模板绑定关系,W,模板绑定信息,测评方案ID、要求模板ID,1
,,密码应用测评要求推进,根据测评要求模板条目结合应用现状，勾选测评要求是否已满足（下拉选项，可选满足、不满足）。已满足的条目需要选择一条对应的测评要求调研选项。不满足的页面自动加载出测评要求改进建议。测评要求调研选项和米平要求改进建议支持在页面中二次编辑。,7.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评要求推进页面勾选测评要求满足状态,密码应用测评要求推进,读取测评要求模板条目,R,测评要求模板条目,条目ID、条目内容,1
,,,,,,,,输入满足状态,E,测评要求状态,条目ID、满足状态（满足/不满足）,1
,,,,,,,,读取满足状态对应的调研选项,R,调研选项,选项ID、选项内容,1
,,,,,,,,输入调研选项,E,调研选项,条目ID、选项ID,1
,,,,,,,,自动加载改进建议,R,改进建议,条目ID、建议内容,1
,,,,,,,,编辑改进建议,E,改进建议,条目ID、建议内容,1
,,,,,,,,保存测评要求推进数据,W,测评要求推进数据,测评方案ID、条目ID、满足状态、选项ID、建议内容,1
,,密码应用测评进度推进,测评管理模块中，密改进度中可结合测评整改进度对测评要求完成进度进行编辑。,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在测评进度推进页面编辑进度信息,密码应用测评进度推进,读取当前进度模板数据,R,进度模板数据,模板ID、进度条目,1
,,,,,,,,输入整改进度信息,E,整改进度,条目ID、当前进度百分比、备注,1
,,,,,,,,保存整改进度信息,W,整改进度,测评方案ID、条目ID、当前进度百分比、备注,1
,,密码应用测评进度跟踪报告展示,自动根据测评推进模块和测评进度模板的数据生成流程进度列表。然后可以编辑列表中的预估完成时间、项目当前进度百分比和备注信息。完成后可以保存当日的流程进度列表,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在进度跟踪报告页面点击生成报告按钮,密码应用测评进度跟踪报告展示,读取测评推进数据,R,测评推进数据,测评方案ID、条目ID、当前进度百分比,1
,,,,,,,,读取进度模板数据,R,进度模板数据,模板ID、进度条目,1
,,,,,,,,生成流程进度列表,X,流程进度列表,条目名称、预估完成时间、当前进度百分比、备注,1
,,密码应用测评进度跟踪报告编辑,针对的是应用测评方案对象，修改密评进度模板详情要素显示或隐藏,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在进度跟踪报告页面编辑报告内容,密码应用测评进度跟踪报告编辑,读取当前报告模板,R,报告模板,模板ID、模板内容,1
,,,,,,,,输入显示/隐藏要素,E,显示要素,要素ID、显示状态（显示/隐藏）,1
,,,,,,,,保存报告模板配置,W,报告模板配置,模板ID、要素ID、显示状态,1
,,密码应用测评进度跟踪报告下载,下载密码应用测评进度的跟踪报告文件,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在进度跟踪报告页面点击下载按钮,密码应用测评进度跟踪报告下载,读取当前报告数据,R,报告数据,测评方案ID、流程进度列表,1
,,,,,,,,生成报告文件,X,报告文件,文件类型（PDF/Excel）、文件内容,1
,,密码应用测评差距分析内容展示,展示密码应用测评过程中当前的差距分析内容,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在差距分析页面查看分析内容,密码应用测评差距分析内容展示,读取差距分析数据,R,差距分析数据,测评方案ID、差距条目、差距描述,1
,,,,,,,,输出差距分析内容,X,差距分析内容,差距条目列表、差距描述,1
,,密码应用测评差距分析内容编辑,编辑密码应用测评过程中当前的差距分析内容,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在差距分析页面编辑分析内容,密码应用测评差距分析内容编辑,读取当前差距分析数据,R,差距分析数据,测评方案ID、差距条目、差距描述,1
,,,,,,,,输入修改后的差距分析内容,E,差距分析数据,差距条目、差距描述,1
,,,,,,,,保存差距分析数据,W,差距分析数据,测评方案ID、差距条目、差距描述,1
,,密码应用测评差距分析内容报告生成,生成密码应用测评过程中当前的差距分析报告文件,5.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在差距分析页面点击生成报告按钮,密码应用测评差距分析内容报告生成,读取差距分析数据,R,差距分析数据,测评方案ID、差距条目、差距描述,1
,,,,,,,,生成报告文件,X,报告文件,文件类型（PDF/Word）、文件内容,1
,,密码应用测评差距分析内容报告导出,导出下载密码应用测评过程中当前的差距分析报告文件,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在差距分析页面点击导出按钮,密码应用测评差距分析内容报告导出,读取差距分析报告数据,R,报告数据,测评方案ID、报告内容,1
,,,,,,,,导出报告文件,X,报告文件,文件类型（Excel/PDF）、文件路径,1
,密评机构管理,密评机构分页列表查询,查看密评机构信息，查看密评机构的机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱。,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员点击密评机构分页列表查询菜单,查看密评机构分页列表信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取密评机构分页数据,R,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,,,,,,,输出密评机构分页列表,X,分页列表数据,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,新增密评机构,在密评机构菜单，点击新增按钮，输入密评机构名称、密评机构地址、密评机构联系人姓名、联系人电话、联系人电子邮箱，点击确认按钮，成功新增密评机构信息。,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密评机构菜单点击新增按钮并输入机构信息,新增密评机构信息,输入新增机构信息,E,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,,,,,,,保存新增机构信息,W,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,编辑密评机构,密评平台可以编辑密评机构信息；在密评机构菜单，点击新增按钮，修改密评机构名称、密评机构地址、密评机构联系人，点击确认按钮，成功编辑密评机构信息。,3.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密评机构菜单点击编辑按钮并修改机构信息,编辑密评机构信息,选择待编辑机构,E,密评机构标识,机构ID,1
,,,,,,,,输入修改后的机构信息,E,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,,,,,,,更新机构信息,W,密评机构信息,机构名称、机构地址、联系人姓名、联系人电话、联系人电子邮箱,1
,,删除密评机构,在密评机构菜单，点击对应密评机构信息操作列删除按钮，弹出确认窗口，点击确定按钮，成功删除密评机构信息。,4.0,发起者：管理员，接收者：密码应用测评管理系统,管理员在密评机构菜单点击删除按钮并确认删除,删除密评机构信息,选择待删除机构,E,密评机构标识,机构ID,1
,,,,,,,,确认删除操作,E,操作确认,确认标识,1
,,,,,,,,执行删除机构信息,W,密评机构信息,机构ID,1
密码应用漏洞/安全事件管理,密码漏洞/安全事件类型管理,密码漏洞/安全事件类型分页列表展示,展示密码漏洞/安全事件类型列表信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码漏洞/安全事件类型分页列表展示菜单,查看密码漏洞/安全事件类型分页列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码漏洞/安全事件类型数据,R,密码漏洞/安全事件类型信息,类型名称、描述、状态、创建时间,1
,,,,,,,,输出分页列表结果,X,分页列表数据,类型名称、描述、状态、创建时间、分页信息,1
,,新增密码漏洞/安全事件类型,新增平台监控的密码漏洞/安全事件类型,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在密码漏洞/安全事件类型管理页面点击新增按钮并填写表单,新增密码漏洞/安全事件类型,输入新增类型信息,E,密码漏洞/安全事件类型信息,类型名称、描述、状态,1
,,,,,,,,校验类型名称唯一性,R,密码漏洞/安全事件类型信息,类型名称,1
,,,,,,,,写入新增类型数据,W,密码漏洞/安全事件类型信息,类型名称、描述、状态,1
,,编辑密码漏洞/安全事件类型,编辑平台监控的密码漏洞/安全事件类型,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在密码漏洞/安全事件类型管理页面点击编辑按钮并修改表单,编辑密码漏洞/安全事件类型,输入类型标识符,E,类型标识符,类型ID,1
,,,,,,,,读取原始类型信息,R,密码漏洞/安全事件类型信息,类型名称、描述、状态,1
,,,,,,,,输入修改后的类型信息,E,密码漏洞/安全事件类型信息,类型名称、描述、状态,1
,,,,,,,,校验修改后类型名称唯一性,R,密码漏洞/安全事件类型信息,类型名称,1
,,,,,,,,写入更新后类型数据,W,密码漏洞/安全事件类型信息,类型ID、类型名称、描述、状态,1
,,删除密码漏洞/安全事件类型,删除平台监控的密码漏洞/安全事件类型,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在密码漏洞/安全事件类型管理页面点击删除按钮,删除密码漏洞/安全事件类型,输入类型标识符,E,类型标识符,类型ID,1
,,,,,,,,读取待删除类型信息,R,密码漏洞/安全事件类型信息,类型ID、类型名称,1
,,,,,,,,写入删除操作记录,W,操作日志,操作类型、操作内容、操作时间,1
,,,,,,,,删除类型数据,W,密码漏洞/安全事件类型信息,类型ID,1
,,初始化密码漏洞/安全事件类型,系统部署时，初始化默认平台监控的密码漏洞/安全事件类型,5.0,发起者：系统部署程序，接收者：密码应用漏洞/安全事件管理系统,系统部署时触发初始化操作,初始化密码漏洞/安全事件类型,读取初始化配置文件,R,初始化配置,类型名称、描述、状态,1
,,,,,,,,校验初始化类型名称唯一性,R,密码漏洞/安全事件类型信息,类型名称,1
,,,,,,,,写入初始化类型数据,W,密码漏洞/安全事件类型信息,类型名称、描述、状态,1
,漏洞/安全事件级别管理,漏洞/安全事件告警分页列表展示,展示平台配置的漏洞/安全事件列表信息,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警分页列表菜单,查看漏洞/安全事件告警分页列表,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取漏洞/安全事件告警列表数据,R,漏洞/安全事件告警信息,漏洞名称、安全事件类型、告警级别、发生时间、状态,1
,,新增漏洞/安全事件告警,新增需要监控的漏洞/安全事件,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件管理页面点击新增告警按钮,新增漏洞/安全事件告警,输入漏洞/安全事件基本信息,E,漏洞/安全事件信息,漏洞名称、安全事件类型、告警级别、描述、监控规则,1
,,,,,,,,保存漏洞/安全事件告警配置,W,漏洞/安全事件告警信息,漏洞名称、安全事件类型、告警级别、描述、监控规则,1
,,漏洞/安全事件告警启用,启用平台监控的漏洞/安全事件,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件告警列表中点击启用按钮,启用漏洞/安全事件告警,选择需要启用的漏洞/安全事件告警,E,漏洞/安全事件标识,漏洞ID、安全事件ID,1
,,,,,,,,更新漏洞/安全事件告警状态为启用,W,漏洞/安全事件状态,漏洞ID、安全事件ID、状态,1
,,漏洞/安全事件告警禁用,禁用平台监控的漏洞/安全事件,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件告警列表中点击禁用按钮,禁用漏洞/安全事件告警,选择需要禁用的漏洞/安全事件告警,E,漏洞/安全事件标识,漏洞ID、安全事件ID,1
,,,,,,,,更新漏洞/安全事件告警状态为禁用,W,漏洞/安全事件状态,漏洞ID、安全事件ID、状态,1
,,删除告警漏洞/安全事件,删除平台监控的漏洞/安全事件,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件告警列表中点击删除按钮,删除漏洞/安全事件告警,选择需要删除的漏洞/安全事件告警,E,漏洞/安全事件标识,漏洞ID、安全事件ID,1
,,,,,,,,删除漏洞/安全事件告警记录,W,漏洞/安全事件信息,漏洞ID、安全事件ID,1
,,漏洞/安全事件告警通知人列表展示,查看监控的漏洞/安全事件触发时通知人信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警通知人列表菜单,查看漏洞/安全事件告警通知人列表,输入通知人查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取漏洞/安全事件告警通知人信息,R,通知人信息,姓名、联系方式、通知方式、关联漏洞ID,1
,,绑定漏洞/安全事件告警通知人,选择监控的漏洞/安全事件触发时通知人,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件告警通知人页面点击绑定按钮,绑定漏洞/安全事件告警通知人,选择漏洞/安全事件告警,E,漏洞/安全事件标识,漏洞ID、安全事件ID,1
,,,,,,,,选择通知人,E,通知人标识,通知人ID,1
,,,,,,,,保存漏洞/安全事件与通知人绑定关系,W,绑定关系,漏洞ID、安全事件ID、通知人ID,1
,,新增漏洞/安全事件告警通知人,新增漏洞/安全事件触发时可通知的人员信息和通知方式,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件告警通知人页面点击新增按钮,新增漏洞/安全事件告警通知人,输入通知人基本信息,E,通知人信息,姓名、联系方式、通知方式,1
,,,,,,,,保存通知人信息,W,通知人信息,姓名、联系方式、通知方式,1
,,删除漏洞/安全事件告警通知人,删除漏洞/安全事件触发时可通知的人员信息和通知方式,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件告警通知人页面点击删除按钮,删除漏洞/安全事件告警通知人,选择需要删除的通知人,E,通知人标识,通知人ID,1
,,,,,,,,删除通知人信息,W,通知人信息,通知人ID,1
,,告警邮箱服务器配置提交,配置漏洞/安全事件触发时告警信息发送的邮箱配置信息,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警邮箱配置页面点击提交按钮,提交告警邮箱服务器配置,输入邮箱服务器配置信息,E,邮箱配置信息,SMTP地址、端口、用户名、密码、加密方式,1
,,,,,,,,保存邮箱服务器配置信息,W,邮箱配置信息,SMTP地址、端口、用户名、密码、加密方式,1
,,告警邮箱服务器配置查询,查询漏洞/安全事件触发时告警信息发送的邮箱配置信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警邮箱配置页面点击查询按钮,查询告警邮箱服务器配置,读取邮箱服务器配置信息,R,邮箱配置信息,SMTP地址、端口、用户名、加密方式,1
,,告警邮箱服务器配置重置,清空漏洞/安全事件触发时告警信息发送的邮箱配置信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警邮箱配置页面点击重置按钮,重置告警邮箱服务器配置,清空邮箱服务器配置信息,W,邮箱配置信息,SMTP地址、端口、用户名、密码、加密方式,1
,,告警验证邮件发送,测试配置的邮箱服务器是否正常，发送测试邮件,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在告警邮箱配置页面点击发送验证邮件按钮,发送告警验证邮件,输入测试邮件接收地址,E,测试邮件信息,收件人邮箱,1
,,,,,,,,调用邮箱服务器发送测试邮件,X,邮件发送请求,SMTP地址、端口、用户名、密码、收件人邮箱,1
,漏洞/安全事件详情管理,漏洞/安全事件基本信息展示,展示平台的漏洞/安全事件信息配置详细信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件基本信息展示菜单,查看漏洞/安全事件基本信息,输入分页查询条件,E,分页信息,页码、单页数量,1
,,,,,,,,读取漏洞/安全事件基本信息,R,漏洞/安全事件基本信息,事件名称、事件描述、发生时间、严重等级、影响范围,1
,,漏洞/安全事件信息编辑,编辑平台的漏洞/安全事件信息配置详细信息,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员在漏洞/安全事件基本信息页面点击编辑按钮,编辑漏洞/安全事件基本信息,输入修改后的漏洞/安全事件信息,E,漏洞/安全事件基本信息,事件名称、事件描述、发生时间、严重等级、影响范围,1
,,,,,,,,更新漏洞/安全事件基本信息,W,漏洞/安全事件基本信息,事件名称、事件描述、发生时间、严重等级、影响范围,1
,,漏洞/安全事件告警阈值配置,设置漏洞/安全事件的判断监控数据的告警阈值配置,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警阈值配置按钮,配置漏洞/安全事件告警阈值,输入告警阈值参数,E,告警阈值配置,阈值名称、监控指标、阈值数值、比较操作符（如>、<、=）,1
,,,,,,,,保存告警阈值配置,W,告警阈值配置,阈值名称、监控指标、阈值数值、比较操作符,1
,,漏洞/安全事件标签配置,设置漏洞/安全事件监控的告警数据标签,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件标签配置按钮,配置漏洞/安全事件告警标签,输入告警标签信息,E,告警标签配置,标签名称、标签类型、关联事件、标签描述,1
,,,,,,,,保存告警标签配置,W,告警标签配置,标签名称、标签类型、关联事件、标签描述,1
,,漏洞/安全事件告警组合阈值配置,配置漏洞/安全事件告警阈值的组合判定方式,7.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击漏洞/安全事件告警组合阈值配置按钮,配置漏洞/安全事件告警组合阈值,输入组合阈值规则,E,组合阈值配置,规则名称、组合逻辑（AND/OR）、关联阈值列表,1
,,,,,,,,保存组合阈值规则,W,组合阈值配置,规则名称、组合逻辑、关联阈值列表,1
,密码产品监控范围管理,密码产品监控列表分布展示,显示平台监控的密码产品对象信息列表,3.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码产品监控列表分布展示菜单,查看密码产品监控对象列表,查询分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码产品监控对象列表,R,密码产品监控对象信息,产品名称、IP地址、端口号、监控状态,1
,,密码产品当前监控数据列表展示,显示监控的密码产品对象的当前监控数据，包含CPU、内存和磁盘信息,4.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码产品当前监控数据列表展示菜单,查看密码产品实时监控数据,查询分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取实时监控数据,R,实时监控数据,产品名称、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,密码产品监控历史数据列表展示,显示监控的密码产品对象的监控历史数据，包含CPU、内存和磁盘信息,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码产品监控历史数据列表展示菜单,查看密码产品历史监控数据,输入时间范围,E,时间范围,开始时间、结束时间,1
,,,,,,,,查询分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取历史监控数据,R,历史监控数据,产品名称、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,密码产品当前监控数据折线图,以折线图信息显示监控的密码产品对象的当前监控数据,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码产品当前监控数据折线图展示菜单,生成密码产品实时监控折线图,读取实时监控数据,R,实时监控数据,产品名称、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,,,,,,,生成折线图数据结构,W,图表数据,时间序列、CPU曲线、内存曲线、磁盘曲线,1
,,密码产品监控历史数据折线图,以折线图信息显示监控的密码产品对象的监控历史数据,5.0,发起者：管理员，接收者：密码应用漏洞/安全事件管理系统,管理员点击密码产品监控历史数据折线图展示菜单,生成密码产品历史监控折线图,输入时间范围,E,时间范围,开始时间、结束时间,1
,,,,,,,,读取历史监控数据,R,历史监控数据,产品名称、CPU使用率、内存使用率、磁盘使用率、监控时间,1
,,,,,,,,生成折线图数据结构,W,图表数据,时间序列、CPU曲线、内存曲线、磁盘曲线,1
数据上报接口,密码应用数据上报类接口,密码应用上报数据采集,收集整理需要上报的密码应用数据,4.0,发起者：系统管理员，接收者：数据上报系统-密码应用数据采集模块,系统管理员启动密码应用数据采集任务,采集密码应用数据,查询数据源配置,E,数据源配置信息,数据源名称、数据源类型、连接参数,1
,,,,,,,,读取原始密码应用数据,R,原始密码应用数据,应用名称、密钥类型、使用场景、更新时间,1
,,,,,,,,转换数据格式,E,格式转换规则,目标格式类型、字段映射规则,1
,,,,,,,,存储结构化数据,W,结构化密码应用数据,标准化字段集合、校验状态,1
,,密码应用数据上报接口对接,和集团平台对接密码应用数据上报接口,6.0,发起者：系统管理员，接收者：数据上报系统-接口对接模块,系统管理员配置集团平台接口参数,对接集团平台接口,输入接口配置参数,E,接口配置信息,接口地址、认证方式、超时时间,1
,,,,,,,,建立接口连接,X,连接状态,连接成功/失败状态码,1
,,,,,,,,发送测试数据包,X,测试数据,测试数据内容、数据格式,1
,,,,,,,,接收接口响应,R,接口响应数据,响应状态码、错误信息,1
,,密码应用上报数据列表展示,以列表形式显示上报的密码应用数据信息,3.0,发起者：用户，接收者：数据上报系统-数据展示模块,用户访问密码应用数据列表页面,展示密码应用数据列表,输入分页参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取过滤条件,E,查询条件,应用名称、密钥类型、时间范围,1
,,,,,,,,查询结构化数据,R,结构化密码应用数据,应用名称、密钥类型、使用场景、更新时间,1
,,,,,,,,输出数据列表,X,展示数据,分页数据集合、总记录数,1
,,密码应用数据定时上报更新                                        ,根据配置的上报频率，定时上报更新密码应用数据,4.0,发起者：定时任务，接收者：数据上报系统-定时上报模块,系统到达配置的上报时间点,定时上报密码应用数据,读取上报配置,R,定时配置,上报频率、目标地址、数据范围,1
,,,,,,,,查询待上报数据,R,待上报数据,应用名称、密钥类型、更新时间,1
,,,,,,,,打包上报数据,E,上报数据包,数据内容、校验码,1
,,,,,,,,发送数据到集团平台,X,上报请求,目标地址、数据包、认证信息,1
,密码资产数据上报类接口,密码产品信息上报数据采集,收集整理需要上报的密码产品信息数据,4.0,发起者：管理员，接收者：数据上报系统,管理员点击密码产品信息上报数据采集菜单,采集密码产品信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取密码产品信息,R,密码产品数据,产品名称、型号、生产厂商、认证状态,1
,,,,,,,,保存采集数据,W,密码产品数据,产品名称、型号、生产厂商、认证状态,1
,,密码产品信息上报接口对接,和集团平台对接密码产品信息数据上报接口,6.0,发起者：管理员，接收者：数据上报系统,管理员配置密码产品信息上报接口参数,对接密码产品信息上报接口,输入接口参数,E,接口配置,接口URL、认证密钥、超时时间,1
,,,,,,,,测试接口连接,X,接口响应,连接状态、错误信息,1
,,,,,,,,保存接口配置,W,接口配置,接口URL、认证密钥、超时时间,1
,,密码产品信息上报数据列表展示,以列表形式显示上报的密码产品信息数据信息,3.0,发起者：管理员，接收者：数据上报系统,管理员访问密码产品信息上报数据列表,展示密码产品信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取密码产品数据,R,密码产品数据,产品名称、型号、上报状态、上报时间,1
,,,,,,,,输出数据列表,X,密码产品列表,产品名称、型号、上报状态、上报时间,1
,,密码产品信息数据定时上报更新,根据配置的上报频率，定时上报更新密码产品信息数据,4.0,发起者：定时任务系统，接收者：数据上报系统,定时任务触发密码产品信息上报,定时上报密码产品信息,读取上报配置,R,定时配置,上报频率、生效时间,1
,,,,,,,,读取待上报数据,R,密码产品数据,产品名称、型号、生产厂商、认证状态,1
,,,,,,,,调用上报接口,X,接口请求,产品数据、上报时间戳,1
,,密钥信息上报数据采集,收集整理需要上报的密钥信息信息数据,4.0,发起者：管理员，接收者：数据上报系统,管理员点击密钥信息上报数据采集菜单,采集密钥信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取密钥信息,R,密钥数据,密钥名称、算法类型、密钥长度、使用状态,1
,,,,,,,,保存采集数据,W,密钥数据,密钥名称、算法类型、密钥长度、使用状态,1
,,密钥信息上报接口对接,和集团平台对接密钥信息信息数据上报接口,6.0,发起者：管理员，接收者：数据上报系统,管理员配置密钥信息上报接口参数,对接密钥信息上报接口,输入接口参数,E,接口配置,接口URL、认证密钥、超时时间,1
,,,,,,,,测试接口连接,X,接口响应,连接状态、错误信息,1
,,,,,,,,保存接口配置,W,接口配置,接口URL、认证密钥、超时时间,1
,,密钥信息上报数据列表展示,以列表形式显示上报的密钥信息信息数据信息,3.0,发起者：管理员，接收者：数据上报系统,管理员访问密钥信息上报数据列表,展示密钥信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取密钥数据,R,密钥数据,密钥名称、算法类型、上报状态、上报时间,1
,,,,,,,,输出数据列表,X,密钥列表,密钥名称、算法类型、上报状态、上报时间,1
,,密钥信息数据定时上报更新,根据配置的上报频率，定时上报更新密钥信息信息数据,4.0,发起者：定时任务系统，接收者：数据上报系统,定时任务触发密钥信息上报,定时上报密钥信息,读取上报配置,R,定时配置,上报频率、生效时间,1
,,,,,,,,读取待上报数据,R,密钥数据,密钥名称、算法类型、密钥长度、使用状态,1
,,,,,,,,调用上报接口,X,接口请求,密钥数据、上报时间戳,1
,,证书信息上报数据采集,收集整理需要上报的证书信息信息数据,4.0,发起者：管理员，接收者：数据上报系统,管理员点击证书信息上报数据采集菜单,采集证书信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取证书信息,R,证书数据,证书名称、颁发机构、有效期、使用状态,1
,,,,,,,,保存采集数据,W,证书数据,证书名称、颁发机构、有效期、使用状态,1
,,证书信息上报接口对接,和集团平台对接证书信息信息数据上报接口,6.0,发起者：管理员，接收者：数据上报系统,管理员配置证书信息上报接口参数,对接证书信息上报接口,输入接口参数,E,接口配置,接口URL、认证密钥、超时时间,1
,,,,,,,,测试接口连接,X,接口响应,连接状态、错误信息,1
,,,,,,,,保存接口配置,W,接口配置,接口URL、认证密钥、超时时间,1
,,证书信息上报数据列表展示,以列表形式显示上报的证书信息信息数据信息,3.0,发起者：管理员，接收者：数据上报系统,管理员访问证书信息上报数据列表,展示证书信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取证书数据,R,证书数据,证书名称、颁发机构、上报状态、上报时间,1
,,,,,,,,输出数据列表,X,证书列表,证书名称、颁发机构、上报状态、上报时间,1
,,证书信息数据定时上报更新,根据配置的上报频率，定时上报更新证书信息信息数据,4.0,发起者：定时任务系统，接收者：数据上报系统,定时任务触发证书信息上报,定时上报证书信息,读取上报配置,R,定时配置,上报频率、生效时间,1
,,,,,,,,读取待上报数据,R,证书数据,证书名称、颁发机构、有效期、使用状态,1
,,,,,,,,调用上报接口,X,接口请求,证书数据、上报时间戳,1
,,密码文档信息上报数据采集,收集整理需要上报的密码文档信息信息数据,4.0,发起者：管理员，接收者：数据上报系统,管理员点击密码文档信息上报数据采集菜单,采集密码文档信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取密码文档信息,R,密码文档数据,文档名称、文档类型、存储路径、更新时间,1
,,,,,,,,保存采集数据,W,密码文档数据,文档名称、文档类型、存储路径、更新时间,1
,,密码文档信息上报接口对接,和集团平台对接密码文档信息信息数据上报接口,6.0,发起者：管理员，接收者：数据上报系统,管理员配置密码文档信息上报接口参数,对接密码文档信息上报接口,输入接口参数,E,接口配置,接口URL、认证密钥、超时时间,1
,,,,,,,,测试接口连接,X,接口响应,连接状态、错误信息,1
,,,,,,,,保存接口配置,W,接口配置,接口URL、认证密钥、超时时间,1
,,密码文档信息上报数据列表展示,以列表形式显示上报的密码文档信息信息数据信息,3.0,发起者：管理员，接收者：数据上报系统,管理员访问密码文档信息上报数据列表,展示密码文档信息数据,查询分页信息,E,分页参数,页码、单页数量,1
,,,,,,,,读取密码文档数据,R,密码文档数据,文档名称、文档类型、上报状态、上报时间,1
,,,,,,,,输出数据列表,X,密码文档列表,文档名称、文档类型、上报状态、上报时间,1
,,密码文档信息数据定时上报更新,根据配置的上报频率，定时上报更新密码文档信息信息数据,4.0,发起者：定时任务系统，接收者：数据上报系统,定时任务触发密码文档信息上报,定时上报密码文档信息,读取上报配置,R,定时配置,上报频率、生效时间,1
,,,,,,,,读取待上报数据,R,密码文档数据,文档名称、文档类型、存储路径、更新时间,1
,,,,,,,,调用上报接口,X,接口请求,密码文档数据、上报时间戳,1
,,密码文档文件上传,上传密码文档文件,5.0,发起者：管理员，接收者：数据上报系统,管理员点击密码文档文件上传按钮,上传密码文档文件,选择上传文件,E,文件信息,文件名称、文件类型、文件大小,1
,,,,,,,,验证文件格式,R,文件校验规则,允许类型、最大大小,1
,,,,,,,,保存上传文件,W,密码文档数据,文档名称、存储路径、上传时间,1
,密码应用测评数据上报类接口,密码应用测评上报数据采集,收集整理需要上报的密码应用测评数据信息数据,4.0,发起者：数据采集模块，接收者：密码应用测评数据上报系统,系统启动或用户手动触发数据采集任务,采集密码应用测评数据,查询数据源配置信息,E,数据源配置,数据源类型、连接参数,1
,,,,,,,,读取原始测评数据,R,原始测评数据,测评指标、测评结果、时间戳,1
,,,,,,,,转换数据格式,W,转换后数据,标准化字段、数据类型,1
,,,,,,,,写入临时存储,W,临时存储数据,文件路径、存储位置,1
,,密码应用测评数据上报接口对接,和集团平台对接密码应用测评数据信息数据上报接口,6.0,发起者：集团平台，接收者：密码应用测评数据上报系统,集团平台调用数据上报接口,对接密码应用测评数据接口,接收接口请求参数,E,接口请求,请求头、认证令牌,1
,,,,,,,,验证数据完整性,R,校验规则,必填字段、数据格式,1
,,,,,,,,处理数据映射关系,W,映射配置,字段对应关系、转换规则,1
,,,,,,,,调用集团平台接口,X,上报数据包,加密数据、签名信息,1
,,密码应用测评上报数据列表展示,以列表形式显示上报的密码应用测评数据信息数据信息,3.0,发起者：用户，接收者：密码应用测评数据上报系统,用户访问数据列表页面,展示密码应用测评数据列表,输入分页查询参数,E,分页信息,页码、单页数量,1
,,,,,,,,读取已上报数据,R,上报记录,上报时间、状态、数据摘要,1
,,,,,,,,生成列表展示数据,W,展示数据,表格结构、排序规则,1
,,,,,,,,输出列表页面,X,HTML响应,页面内容、样式信息,1
,,密码应用测评数据定时上报更新                                        ,根据配置的上报频率，定时上报更新密码应用测评数据信息数据,4.0,发起者：定时任务调度器，接收者：密码应用测评数据上报系统,定时任务触发上报更新,定时上报密码应用测评数据,读取上报配置,R,定时配置,执行周期、优先级,1
,,,,,,,,查询待上报数据,R,待处理数据,数据ID、更新时间,1
,,,,,,,,调用上报接口,X,接口请求,数据包、认证信息,1
,,,,,,,,记录上报日志,W,操作日志,上报时间、结果状态,1
